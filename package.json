{"name": "chronicler", "version": "1.0.0", "description": "AI小说自动化生成系统 - 桌面应用版", "main": "main.js", "scripts": {"start": "chcp 65001 && electron .", "dev": "chcp 65001 && electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux"}, "keywords": ["electron", "ai", "novel", "writing", "automation"], "author": "Chronicler Team", "license": "MIT", "devDependencies": {"electron": "^28.3.3", "electron-builder": "^24.0.0"}, "dependencies": {"front-matter": "^4.0.2", "js-yaml": "^4.1.0", "nunjucks": "^3.2.4", "openai": "^4.0.0", "vue": "^3.3.0"}, "build": {"appId": "com.chronicler.app", "productName": "Chronicler", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}