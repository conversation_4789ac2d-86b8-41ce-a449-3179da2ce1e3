const { app, BrowserWindow, Menu, dialog, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const yaml = require('js-yaml');
const { OpenAI } = require('openai');

// 保持对窗口对象的全局引用，如果不这样做，当JavaScript对象被垃圾回收时，窗口会被自动关闭
let mainWindow;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    show: false
  });

  // 加载应用的index.html
  mainWindow.loadFile('renderer/index.html');

  // 当窗口准备好显示时显示窗口
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // 当窗口被关闭时发出
  mainWindow.on('closed', () => {
    // 取消引用window对象，如果你的应用支持多窗口的话，通常会把多个window对象存放在一个数组里面，与此同时，你应该删除相应的元素
    mainWindow = null;
  });

  // 开发模式下打开开发者工具
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }
}

// Electron会在初始化后并准备创建浏览器窗口时，调用这个函数
app.whenReady().then(createWindow);

// 当全部窗口关闭时退出
app.on('window-all-closed', () => {
  // 在macOS上，除非用户用Cmd + Q确定地退出，否则绝大部分应用及其菜单栏会保持激活
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // 在macOS上，当单击dock图标并且没有其他窗口打开时，通常在应用程序中重新创建一个窗口
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// 创建应用菜单
function createMenu() {
  const template = [
    {
      label: '文件',
      submenu: [
        {
          label: '新建项目',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-new-project');
          }
        },
        {
          label: '打开项目',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            mainWindow.webContents.send('menu-open-project');
          }
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        { role: 'undo', label: '撤销' },
        { role: 'redo', label: '重做' },
        { type: 'separator' },
        { role: 'cut', label: '剪切' },
        { role: 'copy', label: '复制' },
        { role: 'paste', label: '粘贴' }
      ]
    },
    {
      label: '视图',
      submenu: [
        { role: 'reload', label: '重新加载' },
        { role: 'forceReload', label: '强制重新加载' },
        { role: 'toggleDevTools', label: '切换开发者工具' },
        { type: 'separator' },
        { role: 'resetZoom', label: '实际大小' },
        { role: 'zoomIn', label: '放大' },
        { role: 'zoomOut', label: '缩小' },
        { type: 'separator' },
        { role: 'togglefullscreen', label: '切换全屏' }
      ]
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于 Chronicler',
              message: 'Chronicler v1.0.0',
              detail: 'AI小说自动化生成系统\n\n基于约定优于配置的原则，让创作者专注于故事本身。'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// 当应用准备就绪时创建菜单
app.whenReady().then(() => {
  createMenu();
});

// IPC处理程序
ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

ipcMain.handle('read-file', async (event, filePath) => {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('write-file', async (event, filePath, data) => {
  try {
    await fs.writeFile(filePath, data, 'utf8');
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('read-yaml', async (event, filePath) => {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    const parsed = yaml.load(data);
    return { success: true, data: parsed };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('write-yaml', async (event, filePath, data) => {
  try {
    const yamlStr = yaml.dump(data, { indent: 2 });
    await fs.writeFile(filePath, yamlStr, 'utf8');
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('create-directory', async (event, dirPath) => {
  try {
    await fs.mkdir(dirPath, { recursive: true });
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('check-path-exists', async (event, path) => {
  try {
    await fs.access(path);
    return { success: true, exists: true };
  } catch (error) {
    return { success: true, exists: false };
  }
});

ipcMain.handle('list-directory', async (event, dirPath) => {
  try {
    const files = await fs.readdir(dirPath, { withFileTypes: true });
    const result = files.map(file => ({
      name: file.name,
      isDirectory: file.isDirectory(),
      isFile: file.isFile()
    }));
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('delete-file', async (event, filePath) => {
  try {
    await fs.unlink(filePath);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// AI生成相关的处理程序
ipcMain.handle('generate-scene-draft', async (event, projectPath, sceneData) => {
  try {
    const result = await generateSceneDraft(projectPath, sceneData);
    return result;
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('analyze-and-update-state', async (event, projectPath, sceneId, content) => {
  try {
    const result = await analyzeSceneAndUpdateState(projectPath, sceneId, content);
    return result;
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// AI生成场景初稿的核心函数
async function generateSceneDraft(projectPath, sceneData) {
  try {
    // 1. 读取项目配置
    const configPath = path.join(projectPath, 'chronicler.yml');
    const configContent = await fs.readFile(configPath, 'utf8');
    const config = yaml.load(configContent);

    if (!config.api_settings?.api_key) {
      return { success: false, error: '请先在项目配置中设置API密钥' };
    }

    // 2. 初始化OpenAI客户端
    const openaiConfig = {
      apiKey: config.api_settings.api_key
    };

    if (config.api_settings.base_url && config.api_settings.base_url !== 'https://api.openai.com/v1') {
      openaiConfig.baseURL = config.api_settings.base_url;
    }

    const openai = new OpenAI(openaiConfig);

    // 3. 汇编上下文信息
    const context = await compileSceneContext(projectPath, sceneData);

    // 4. 构建prompt
    const prompt = buildScenePrompt(context, sceneData);

    // 5. 调用AI生成
    const completion = await openai.chat.completions.create({
      model: config.api_settings.model || 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: '你是一个专业的小说写作助手。请根据提供的场景信息和上下文，生成一个生动、符合角色性格和故事背景的场景文本。'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: config.api_settings.temperature || 0.7
    });

    const generatedText = completion.choices[0].message.content;

    // 6. 保存生成的初稿
    const draftPath = path.join(projectPath, '3_manuscript', 'drafts', `${sceneData.scene_id}.md`);
    const draftContent = `# ${sceneData.scene_id}

> 场景ID: ${sceneData.scene_id}
> 故事线: ${sceneData.storyline}
> 生成时间: ${new Date().toLocaleString()}

${generatedText}
`;

    await fs.writeFile(draftPath, draftContent, 'utf8');

    return {
      success: true,
      draftPath,
      content: generatedText
    };

  } catch (error) {
    console.error('AI生成失败:', error);
    return { success: false, error: error.message };
  }
}

// 汇编场景上下文信息
async function compileSceneContext(projectPath, sceneData) {
  const context = {
    worldBible: '',
    characters: {},
    locations: {},
    previousScenes: []
  };

  try {
    // 读取世界观设定
    const worldBiblePath = path.join(projectPath, '1_knowledge_base', 'world_bible.md');
    try {
      context.worldBible = await fs.readFile(worldBiblePath, 'utf8');
    } catch (e) {
      // 文件不存在时忽略
    }

    // 读取相关角色信息
    if (sceneData.characters && sceneData.characters.length > 0) {
      for (const characterId of sceneData.characters) {
        const charPath = path.join(projectPath, '1_knowledge_base', 'characters', `${characterId}.yml`);
        try {
          const charContent = await fs.readFile(charPath, 'utf8');
          context.characters[characterId] = yaml.load(charContent);
        } catch (e) {
          console.warn(`无法读取角色文件: ${characterId}`);
        }
      }
    }

    // 读取场景地点信息
    if (sceneData.location) {
      const locationPath = path.join(projectPath, '1_knowledge_base', 'locations', `${sceneData.location}.yml`);
      try {
        const locationContent = await fs.readFile(locationPath, 'utf8');
        context.locations[sceneData.location] = yaml.load(locationContent);
      } catch (e) {
        console.warn(`无法读取地点文件: ${sceneData.location}`);
      }
    }

    // 读取已发布的场景作为上下文（最近3个）
    const publishedDir = path.join(projectPath, '3_manuscript', 'published');
    try {
      const publishedFiles = await fs.readdir(publishedDir);
      const mdFiles = publishedFiles.filter(f => f.endsWith('.md')).slice(-3);

      for (const file of mdFiles) {
        const filePath = path.join(publishedDir, file);
        const content = await fs.readFile(filePath, 'utf8');
        context.previousScenes.push({
          filename: file,
          content: content
        });
      }
    } catch (e) {
      // 目录不存在时忽略
    }

  } catch (error) {
    console.error('汇编上下文失败:', error);
  }

  return context;
}

// 构建场景生成的prompt
function buildScenePrompt(context, sceneData) {
  let prompt = `请为以下场景生成一个生动的小说片段：

## 场景信息
- 场景ID: ${sceneData.scene_id}
- 故事线: ${sceneData.storyline}
- 地点: ${sceneData.location || '未指定'}

## 场景目标
`;

  if (sceneData.goal && sceneData.goal.length > 0) {
    sceneData.goal.forEach(goal => {
      prompt += `- ${goal.type}: ${goal.content}\n`;
    });
  } else {
    prompt += '- 未指定具体目标\n';
  }

  // 添加角色信息
  if (Object.keys(context.characters).length > 0) {
    prompt += '\n## 参与角色\n';
    Object.entries(context.characters).forEach(([id, character]) => {
      prompt += `### ${character.name || id}\n`;
      if (character.archetype) prompt += `- 原型: ${character.archetype}\n`;
      if (character.motivation) prompt += `- 动机: ${character.motivation}\n`;
      if (character.state?.emotion) prompt += `- 当前情绪: ${character.state.emotion}\n`;
      if (character.state?.location) prompt += `- 当前位置: ${character.state.location}\n`;
      prompt += '\n';
    });
  }

  // 添加地点信息
  if (Object.keys(context.locations).length > 0) {
    prompt += '\n## 场景地点\n';
    Object.entries(context.locations).forEach(([id, location]) => {
      prompt += `### ${location.name || id}\n`;
      if (location.description) prompt += `- 描述: ${location.description}\n`;
      if (location.atmosphere) prompt += `- 氛围: ${location.atmosphere}\n`;
      prompt += '\n';
    });
  }

  // 添加世界观背景
  if (context.worldBible) {
    prompt += '\n## 世界观背景\n';
    const worldBibleSummary = context.worldBible;
    prompt += worldBibleSummary + '\n';
  }

  // 添加前置场景上下文
  if (context.previousScenes.length > 0) {
    prompt += '\n## 前置场景参考\n';
    context.previousScenes.forEach((scene, index) => {
      prompt += `### 场景 ${index + 1}\n`;
      prompt += scene.content + '\n\n';
    });
  }

  prompt += `
## 写作要求
1. 生成2000字左右的章节文本
2. 保持角色性格一致性
3. 体现场景目标中的要求
4. 使用生动的描写和口语化的对话，文笔风格使用网络小说风格（起点、晋江等），不能使用代码来进行表达。
5. 符合已建立的世界观设定,
6. 为后续情节发展留下合适的铺垫
7、世界观是用于编织剧情，不能提前透露，具体透露多少根据当前处于的主线剧情决定。

请开始创作这个场景：`;

  return prompt;
}

// 分析场景并更新角色状态的核心函数
async function analyzeSceneAndUpdateState(projectPath, sceneId, content) {
  try {
    // 1. 读取项目配置
    const configPath = path.join(projectPath, 'chronicler.yml');
    const configContent = await fs.readFile(configPath, 'utf8');
    const config = yaml.load(configContent);

    if (!config.api_settings?.api_key) {
      return { success: false, error: '请先在项目配置中设置API密钥' };
    }

    // 2. 初始化OpenAI客户端
    const openaiConfig = {
      apiKey: config.api_settings.api_key
    };

    if (config.api_settings.base_url && config.api_settings.base_url !== 'https://api.openai.com/v1') {
      openaiConfig.baseURL = config.api_settings.base_url;
    }

    const openai = new OpenAI(openaiConfig);

    // 3. 读取场景卡片信息
    const sceneCardPath = path.join(projectPath, '2_plot', 'scene_cards', `${sceneId}.md`);
    let sceneData = {};
    try {
      const sceneCardContent = await fs.readFile(sceneCardPath, 'utf8');
      const frontmatterMatch = sceneCardContent.match(/^---\n([\s\S]*?)\n---/);
      if (frontmatterMatch) {
        sceneData = parseSimpleYaml(frontmatterMatch[1]);
      }
    } catch (e) {
      console.warn('无法读取场景卡片:', sceneId);
    }

    // 4. 构建分析prompt
    const analysisPrompt = buildAnalysisPrompt(sceneData, content);

    // 5. 调用AI进行分析
    const completion = await openai.chat.completions.create({
      model: config.api_settings.model || 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: '你是一个专业的小说分析助手。请分析提供的场景内容，提取关键的状态变化信息，并以YAML格式返回结构化的摘要。'
        },
        {
          role: 'user',
          content: analysisPrompt
        }
      ],
      temperature: 0.3, // 使用较低的temperature确保输出稳定
      max_tokens: 1500
    });

    const analysisResult = completion.choices[0].message.content;

    // 6. 解析AI返回的YAML
    let summary;
    try {
      // 提取YAML部分
      const yamlMatch = analysisResult.match(/```yaml\n([\s\S]*?)\n```/) ||
                       analysisResult.match(/```\n([\s\S]*?)\n```/) ||
                       analysisResult.match(/```yaml.*?```/);

      if (yamlMatch) {
        summary = yaml.load(yamlMatch[1]);
      } else {
        console.error('AI响应中未找到有效的YAML代码块，原始响应：', completion.choices[0].message);
        return { success: false, error: '无法解析AI分析结果' };
      }
    } catch (parseError) {
      console.error('解析AI分析结果失败:', parseError);
      return { success: false, error: '无法解析AI分析结果' };
    }

    // 7. 保存摘要文件
    const summaryPath = path.join(projectPath, '4_summaries', `${sceneId}.yml`);
    await fs.writeFile(summaryPath, yaml.dump(summary, { indent: 2 }), 'utf8');

    // 8. 更新角色状态
    await updateCharacterStates(projectPath, summary);

    return {
      success: true,
      summary,
      summaryPath
    };

  } catch (error) {
    console.error('分析场景失败:', error);
    return { success: false, error: error.message };
  }
}

// 构建分析prompt
function buildAnalysisPrompt(sceneData, content) {
  let prompt = `请分析以下小说场景，提取关键的状态变化信息：

## 场景信息
- 场景ID: ${sceneData.scene_id || '未知'}
- 故事线: ${sceneData.storyline || 'main'}
- 参与角色: ${sceneData.characters ? sceneData.characters.join(', ') : '未知'}
- 地点: ${sceneData.location || '未知'}

## 场景内容
${content}

## 分析要求
请以YAML格式返回分析结果，包含以下信息：

\`\`\`yaml
scene_id: "${sceneData.scene_id || 'unknown'}"
next_scene_recommendation: "对下一个场景的建议"

# 角色状态变更
state_updates:
  角色ID:
    emotion: "新的情绪状态"
    location: "新的位置（如果有变化）"
    inventory_add: ["新增的物品"]
    inventory_remove: ["移除的物品"]

# 角色关系变更
relationship_updates:
  角色ID:
    其他角色ID:
      description: "关系描述"
      status: "关系状态（如Trusting, Strained, Romantic, Hostile等）"
      knowledge_level: 数字(0-10)

# 新揭露的关键信息
new_information_revealed:
  - "系统面板（有的话，实时更新）"
  - "重要信息1"
  - "重要信息2"
\`\`\`

注意：
1. 只记录实际发生变化的状态
2. 情绪状态要具体（如Happy, Sad, Angry, Confused等）
3. 关系状态变化要有依据
4. 新信息要是对故事发展重要的内容`;

  return prompt;
}

// 更新角色状态
async function updateCharacterStates(projectPath, summary) {
  if (!summary.state_updates) return;

  for (const [characterId, updates] of Object.entries(summary.state_updates)) {
    try {
      const characterPath = path.join(projectPath, '1_knowledge_base', 'characters', `${characterId}.yml`);

      // 读取现有角色数据
      let characterData;
      try {
        const characterContent = await fs.readFile(characterPath, 'utf8');
        characterData = yaml.load(characterContent);
      } catch (e) {
        console.warn(`角色文件不存在: ${characterId}`);
        continue;
      }

      // 更新状态
      if (!characterData.state) characterData.state = {};

      if (updates.emotion) {
        characterData.state.emotion = updates.emotion;
      }

      if (updates.location) {
        characterData.state.location = updates.location;
      }

      if (updates.inventory_add) {
        if (!characterData.state.inventory) characterData.state.inventory = [];
        characterData.state.inventory.push(...updates.inventory_add);
      }

      if (updates.inventory_remove) {
        if (characterData.state.inventory) {
          characterData.state.inventory = characterData.state.inventory.filter(
            item => !updates.inventory_remove.includes(item)
          );
        }
      }

      // 更新关系
      if (summary.relationship_updates && summary.relationship_updates[characterId]) {
        if (!characterData.relationships) characterData.relationships = {};

        for (const [otherCharId, relationship] of Object.entries(summary.relationship_updates[characterId])) {
          characterData.relationships[otherCharId] = relationship;
        }
      }

      // 保存更新后的角色数据
      await fs.writeFile(characterPath, yaml.dump(characterData, { indent: 2 }), 'utf8');

    } catch (error) {
      console.error(`更新角色状态失败: ${characterId}`, error);
    }
  }
}

// 简单的YAML解析器
function parseSimpleYaml(yamlContent) {
  const result = {};
  const lines = yamlContent.split('\n');
  let currentKey = null;
  let currentArray = null;

  for (const line of lines) {
    const trimmed = line.trim();
    if (!trimmed || trimmed.startsWith('#')) continue;

    if (trimmed.startsWith('- ')) {
      // 数组项
      if (currentArray) {
        const item = trimmed.substring(2).trim();
        currentArray.push(item);
      }
    } else if (trimmed.includes(':')) {
      // 键值对
      const colonIndex = trimmed.indexOf(':');
      const key = trimmed.substring(0, colonIndex).trim();
      const value = trimmed.substring(colonIndex + 1).trim();

      if (value === '') {
        // 可能是数组的开始
        currentKey = key;
        currentArray = [];
        result[key] = currentArray;
      } else {
        result[key] = value;
        currentKey = null;
        currentArray = null;
      }
    }
  }

  return result;
}
