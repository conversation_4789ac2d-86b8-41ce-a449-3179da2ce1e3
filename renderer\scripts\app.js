// Vue应用主逻辑
const { createApp } = Vue;

createApp({
    data() {
        return {
            // 当前激活的视图
            activeView: 'dashboard',
            
            // 当前项目信息
            currentProject: null,
            
            // 项目统计数据
            projectStats: {
                characters: 0,
                locations: 0,
                organizations: 0,
                scenes: 0,
                drafts: 0,
                published: 0
            },

            // 知识库数据
            characters: [],
            locations: [],
            organizations: [],
            scenes: [],
            drafts: [],
            publishedChapters: [],

            // 对话框状态
            showCharacterDialog: false,
            showLocationDialog: false,
            showOrganizationDialog: false,
            showSceneDialog: false,
            showApiDialog: false,
            showEditorDialog: false,
            editingCharacter: null,
            editingLocation: null,
            editingOrganization: null,
            editingScene: null,
            editingDraft: null,

            // API配置状态
            apiConfigured: false,
            isGenerating: false,

            // 编辑器状态
            editorContent: '',
            isSaved: true,
            autoSaveTimer: null,

            // 生成状态文本
            generatingText: 'AI正在工作...',
            generatingSubtext: '请耐心等待',

            // 通知系统
            notification: {
                show: false,
                type: 'info', // success, error, warning, info
                icon: 'ℹ️',
                message: '',
                timer: null
            },

            // 世界观设定
            worldBibleContent: '',
            worldBibleChanged: false,
            worldBibleAutoSaveTimer: null,

            // 主线大纲
            outlineContent: '',
            outlineChanged: false,
            outlineAutoSaveTimer: null,

            // 表单数据
            characterForm: {
                name: '',
                archetype: '',
                motivation: '',
                appearance: '',
                emotion: '',
                location: ''
            },
            locationForm: {
                name: '',
                type: '',
                description: '',
                atmosphere: ''
            },
            organizationForm: {
                name: '',
                type: '',
                description: '',
                leader: '',
                members: [],
                influence_level: '',
                status: '',
                headquarters: '',
                resources: ''
            },
            sceneForm: {
                scene_id: '',
                storyline: 'main',
                characters: [],
                location: '',
                goals: [],
                description: ''
            },
            apiForm: {
                base_url: '',
                api_key: '',
                model: 'gpt-3.5-turbo',
                temperature: 0.7
            }
        };
    },

    computed: {
        wordCount() {
            return this.editorContent.length;
        },
        worldBibleWordCount() {
            return this.worldBibleContent.length;
        },
        outlineWordCount() {
            return this.outlineContent.length;
        }
    },

    methods: {
        // 设置激活视图
        setActiveView(view) {
            this.activeView = view;
        },

        // 通知系统方法
        showNotification(message, type = 'info', duration = 5000) {
            // 清除之前的定时器
            if (this.notification.timer) {
                clearTimeout(this.notification.timer);
            }

            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };

            this.notification = {
                show: true,
                type,
                icon: icons[type] || icons.info,
                message,
                timer: null
            };

            // 自动隐藏
            if (duration > 0) {
                this.notification.timer = setTimeout(() => {
                    this.hideNotification();
                }, duration);
            }
        },

        hideNotification() {
            this.notification.show = false;
            if (this.notification.timer) {
                clearTimeout(this.notification.timer);
                this.notification.timer = null;
            }
        },
        
        // 获取视图标题
        getViewTitle() {
            const titles = {
                dashboard: '项目概览',
                characters: '角色管理',
                locations: '地点管理',
                organizations: '组织管理',
                worldbible: '世界观设定',
                outline: '主线大纲',
                scenes: '场景卡片',
                drafts: '草稿箱',
                published: '已发布'
            };
            return titles[this.activeView] || '未知视图';
        },
        
        // 创建新项目
        async createNewProject() {
            try {
                const result = await window.electronAPI.showSaveDialog({
                    title: '选择项目保存位置',
                    defaultPath: 'my_novel',
                    properties: ['createDirectory']
                });
                
                if (!result.canceled && result.filePath) {
                    await this.initializeProject(result.filePath);
                }
            } catch (error) {
                console.error('创建项目失败:', error);
                this.showNotification('创建项目失败: ' + error.message, 'error');
            }
        },
        
        // 打开现有项目
        async openExistingProject() {
            try {
                const result = await window.electronAPI.showOpenDialog({
                    title: '选择项目文件夹',
                    properties: ['openDirectory']
                });
                
                if (!result.canceled && result.filePaths.length > 0) {
                    await this.loadProject(result.filePaths[0]);
                }
            } catch (error) {
                console.error('打开项目失败:', error);
                alert('打开项目失败: ' + error.message);
            }
        },
        
        // 初始化新项目
        async initializeProject(projectPath) {
            try {
                // 创建项目目录结构
                const directories = [
                    '1_knowledge_base/characters',
                    '1_knowledge_base/locations',
                    '1_knowledge_base/organizations',
                    '2_plot/scene_cards',
                    '3_manuscript/drafts',
                    '3_manuscript/published',
                    '4_summaries'
                ];
                
                for (const dir of directories) {
                    await window.electronAPI.createDirectory(`${projectPath}/${dir}`);
                }
                
                // 创建主配置文件
                const projectName = projectPath.split(/[/\\]/).pop();
                const config = {
                    name: projectName,
                    version: '1.0.0',
                    author: '',
                    description: '',
                    created_at: new Date().toISOString(),
                    api_settings: {
                        base_url: 'https://api.openai.com/v1',
                        api_key: '',
                        model: 'gpt-3.5-turbo',
                        temperature: 0.7
                    }
                };
                
                await window.electronAPI.writeYaml(`${projectPath}/chronicler.yml`, config);
                
                // 创建世界观设定模板
                const worldBibleTemplate = `# 世界观设定

## 基本设定

### 时代背景
- 时间：
- 地点：
- 社会制度：

### 物理法则
- 魔法系统：
- 科技水平：
- 特殊规则：

## 历史背景

### 重要事件
- 

### 传说故事
- 

## 文化设定

### 语言文字
- 

### 宗教信仰
- 

### 社会习俗
- 
`;
                
                await window.electronAPI.writeFile(`${projectPath}/1_knowledge_base/world_bible.md`, worldBibleTemplate);
                
                // 创建主线大纲模板
                const outlineTemplate = `# 主线大纲

## 故事概述
- 

## 主要角色
- 

## 情节结构

### 第一幕：开端
- 

### 第二幕：发展
- 

### 第三幕：高潮
- 

### 第四幕：结局
- 

## 主题思想
- 
`;
                
                await window.electronAPI.writeFile(`${projectPath}/2_plot/main_outline.md`, outlineTemplate);
                
                // 加载项目
                await this.loadProject(projectPath);

                this.showNotification('项目创建成功！', 'success');
            } catch (error) {
                console.error('初始化项目失败:', error);
                throw error;
            }
        },
        
        // 加载项目
        async loadProject(projectPath) {
            try {
                // 检查项目配置文件是否存在
                const configPath = `${projectPath}/chronicler.yml`;
                const configExists = await window.electronAPI.checkPathExists(configPath);
                
                if (!configExists.exists) {
                    throw new Error('这不是一个有效的Chronicler项目文件夹');
                }
                
                // 读取项目配置
                const configResult = await window.electronAPI.readYaml(configPath);
                if (!configResult.success) {
                    throw new Error('无法读取项目配置文件');
                }
                
                // 设置当前项目
                this.currentProject = {
                    name: configResult.data.name,
                    path: projectPath,
                    config: configResult.data
                };
                
                // 更新项目统计
                await this.updateProjectStats();

                // 加载知识库数据
                await this.loadCharacters();
                await this.loadLocations();
                await this.loadOrganizations();
                await this.loadScenes();
                await this.loadDrafts();
                await this.loadPublishedChapters();
                await this.loadWorldBible();
                await this.loadOutline();

                // 检查API配置
                this.checkApiConfiguration();
                
            } catch (error) {
                console.error('加载项目失败:', error);
                throw error;
            }
        },
        
        // 更新项目统计
        async updateProjectStats() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;

                // 统计角色数量
                const charactersResult = await window.electronAPI.listDirectory(`${basePath}/1_knowledge_base/characters`);
                this.projectStats.characters = charactersResult.success ?
                    charactersResult.data.filter(f => f.isFile && f.name.endsWith('.yml')).length : 0;

                // 统计地点数量
                const locationsResult = await window.electronAPI.listDirectory(`${basePath}/1_knowledge_base/locations`);
                this.projectStats.locations = locationsResult.success ?
                    locationsResult.data.filter(f => f.isFile && f.name.endsWith('.yml')).length : 0;

                // 统计组织数量
                const organizationsResult = await window.electronAPI.listDirectory(`${basePath}/1_knowledge_base/organizations`);
                this.projectStats.organizations = organizationsResult.success ?
                    organizationsResult.data.filter(f => f.isFile && f.name.endsWith('.yml')).length : 0;

                // 统计场景数量
                const scenesResult = await window.electronAPI.listDirectory(`${basePath}/2_plot/scene_cards`);
                this.projectStats.scenes = scenesResult.success ?
                    scenesResult.data.filter(f => f.isFile && f.name.endsWith('.md')).length : 0;

                // 统计草稿数量
                const draftsResult = await window.electronAPI.listDirectory(`${basePath}/3_manuscript/drafts`);
                this.projectStats.drafts = draftsResult.success ?
                    draftsResult.data.filter(f => f.isFile && f.name.endsWith('.md')).length : 0;

                // 统计已发布数量
                const publishedResult = await window.electronAPI.listDirectory(`${basePath}/3_manuscript/published`);
                this.projectStats.published = publishedResult.success ?
                    publishedResult.data.filter(f => f.isFile && f.name.endsWith('.md')).length : 0;

            } catch (error) {
                console.error('更新项目统计失败:', error);
            }
        },

        // 加载角色数据
        async loadCharacters() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;
                const charactersDir = `${basePath}/1_knowledge_base/characters`;

                const result = await window.electronAPI.listDirectory(charactersDir);
                if (!result.success) {
                    this.characters = [];
                    return;
                }

                const characterFiles = result.data.filter(f => f.isFile && f.name.endsWith('.yml'));
                const characters = [];

                for (const file of characterFiles) {
                    const filePath = `${charactersDir}/${file.name}`;
                    const yamlResult = await window.electronAPI.readYaml(filePath);

                    if (yamlResult.success) {
                        const character = yamlResult.data;
                        character.id = file.name.replace('.yml', '');
                        character.filename = file.name;
                        characters.push(character);
                    }
                }

                this.characters = characters;
            } catch (error) {
                console.error('加载角色数据失败:', error);
                this.characters = [];
            }
        },

        // 加载地点数据
        async loadLocations() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;
                const locationsDir = `${basePath}/1_knowledge_base/locations`;

                const result = await window.electronAPI.listDirectory(locationsDir);
                if (!result.success) {
                    this.locations = [];
                    return;
                }

                const locationFiles = result.data.filter(f => f.isFile && f.name.endsWith('.yml'));
                const locations = [];

                for (const file of locationFiles) {
                    const filePath = `${locationsDir}/${file.name}`;
                    const yamlResult = await window.electronAPI.readYaml(filePath);

                    if (yamlResult.success) {
                        const location = yamlResult.data;
                        location.id = file.name.replace('.yml', '');
                        location.filename = file.name;
                        locations.push(location);
                    }
                }

                this.locations = locations;
            } catch (error) {
                console.error('加载地点数据失败:', error);
                this.locations = [];
            }
        },

        // 加载组织数据
        async loadOrganizations() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;
                const organizationsDir = `${basePath}/1_knowledge_base/organizations`;

                const result = await window.electronAPI.listDirectory(organizationsDir);
                if (!result.success) {
                    this.organizations = [];
                    return;
                }

                const organizationFiles = result.data.filter(f => f.isFile && f.name.endsWith('.yml'));
                const organizations = [];

                for (const file of organizationFiles) {
                    const filePath = `${organizationsDir}/${file.name}`;
                    const yamlResult = await window.electronAPI.readYaml(filePath);

                    if (yamlResult.success) {
                        const organization = yamlResult.data;
                        organization.id = file.name.replace('.yml', '');
                        organization.filename = file.name;
                        organizations.push(organization);
                    }
                }

                this.organizations = organizations;
            } catch (error) {
                console.error('加载组织数据失败:', error);
                this.organizations = [];
            }
        },

        // 加载场景数据
        async loadScenes() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;
                const scenesDir = `${basePath}/2_plot/scene_cards`;

                const result = await window.electronAPI.listDirectory(scenesDir);
                if (!result.success) {
                    this.scenes = [];
                    return;
                }

                const sceneFiles = result.data.filter(f => f.isFile && f.name.endsWith('.md'));
                const scenes = [];

                for (const file of sceneFiles) {
                    const filePath = `${scenesDir}/${file.name}`;
                    const fileResult = await window.electronAPI.readFile(filePath);

                    if (fileResult.success) {
                        // 解析Markdown文件的frontmatter
                        const content = fileResult.data;
                        const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/);

                        if (frontmatterMatch) {
                            try {
                                // 使用简单的YAML解析
                                const yamlContent = frontmatterMatch[1];
                                const scene = this.parseSimpleYaml(yamlContent);
                                const description = frontmatterMatch[2].trim();

                                scene.id = file.name.replace('.md', '');
                                scene.filename = file.name;
                                scene.description = description;

                                // 检查是否有对应的草稿
                                const draftPath = `${basePath}/3_manuscript/drafts/${scene.scene_id}.md`;
                                const draftExists = await window.electronAPI.checkPathExists(draftPath);
                                scene.hasDraft = draftExists.exists;

                                scenes.push(scene);
                            } catch (parseError) {
                                console.error('解析场景文件失败:', file.name, parseError);
                            }
                        }
                    }
                }

                this.scenes = scenes;
            } catch (error) {
                console.error('加载场景数据失败:', error);
                this.scenes = [];
            }
        },

        // 简单的YAML解析器（仅用于场景卡片）
        parseSimpleYaml(yamlContent) {
            const result = {};
            const lines = yamlContent.split('\n');
            let currentKey = null;
            let currentArray = null;

            for (const line of lines) {
                const trimmed = line.trim();
                if (!trimmed || trimmed.startsWith('#')) continue;

                if (trimmed.startsWith('- ')) {
                    // 数组项
                    if (currentArray) {
                        const item = trimmed.substring(2).trim();
                        if (item.includes(':')) {
                            // 对象数组项
                            const [key, value] = item.split(':').map(s => s.trim());
                            const obj = {};
                            obj[key] = value;
                            currentArray.push(obj);
                        } else {
                            currentArray.push(item);
                        }
                    }
                } else if (trimmed.includes(':')) {
                    // 键值对
                    const colonIndex = trimmed.indexOf(':');
                    const key = trimmed.substring(0, colonIndex).trim();
                    const value = trimmed.substring(colonIndex + 1).trim();

                    if (value === '') {
                        // 可能是数组的开始
                        currentKey = key;
                        currentArray = [];
                        result[key] = currentArray;
                    } else {
                        result[key] = value;
                        currentKey = null;
                        currentArray = null;
                    }
                }
            }

            return result;
        },

        // 加载草稿数据
        async loadDrafts() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;
                const draftsDir = `${basePath}/3_manuscript/drafts`;

                const result = await window.electronAPI.listDirectory(draftsDir);
                if (!result.success) {
                    this.drafts = [];
                    return;
                }

                const draftFiles = result.data.filter(f => f.isFile && f.name.endsWith('.md'));
                const drafts = [];

                for (const file of draftFiles) {
                    const filePath = `${draftsDir}/${file.name}`;
                    const fileResult = await window.electronAPI.readFile(filePath);

                    if (fileResult.success) {
                        const content = fileResult.data;
                        const title = file.name.replace('.md', '');
                        const preview = content.substring(0, 200).replace(/[#>\n]/g, ' ').trim() + '...';

                        // 获取文件修改时间（简化处理）
                        const lastModified = new Date().toLocaleDateString();

                        drafts.push({
                            id: title,
                            title,
                            filename: file.name,
                            content,
                            preview,
                            lastModified
                        });
                    }
                }

                this.drafts = drafts;
            } catch (error) {
                console.error('加载草稿数据失败:', error);
                this.drafts = [];
            }
        },

        // 加载已发布章节数据
        async loadPublishedChapters() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;
                const publishedDir = `${basePath}/3_manuscript/published`;

                const result = await window.electronAPI.listDirectory(publishedDir);
                if (!result.success) {
                    this.publishedChapters = [];
                    return;
                }

                const publishedFiles = result.data.filter(f => f.isFile && f.name.endsWith('.md'));
                const chapters = [];

                for (const file of publishedFiles) {
                    const filePath = `${publishedDir}/${file.name}`;
                    const fileResult = await window.electronAPI.readFile(filePath);

                    if (fileResult.success) {
                        const content = fileResult.data;
                        const title = file.name.replace('.md', '');
                        const preview = content.substring(0, 200).replace(/[#>\n]/g, ' ').trim() + '...';

                        // 获取文件修改时间（简化处理）
                        const publishedAt = new Date().toLocaleDateString();

                        chapters.push({
                            id: title,
                            title,
                            filename: file.name,
                            content,
                            preview,
                            publishedAt
                        });
                    }
                }

                this.publishedChapters = chapters;
            } catch (error) {
                console.error('加载已发布章节失败:', error);
                this.publishedChapters = [];
            }
        },

        // 角色管理方法
        showCreateCharacterDialog() {
            this.editingCharacter = null;
            this.characterForm = {
                name: '',
                archetype: '',
                motivation: '',
                appearance: '',
                emotion: '',
                location: ''
            };
            this.showCharacterDialog = true;
        },

        editCharacter(character) {
            this.editingCharacter = character;
            this.characterForm = {
                name: character.name || '',
                archetype: character.archetype || '',
                motivation: character.motivation || '',
                appearance: character.appearance || '',
                emotion: character.state?.emotion || '',
                location: character.state?.location || ''
            };
            this.showCharacterDialog = true;
        },

        closeCharacterDialog() {
            this.showCharacterDialog = false;
            this.editingCharacter = null;
        },

        async saveCharacter() {
            if (!this.characterForm.name.trim()) {
                this.showNotification('请输入角色名称', 'warning');
                return;
            }

            // 验证角色名称是否已存在（编辑模式除外）
            if (!this.editingCharacter) {
                const existingCharacter = this.characters.find(c =>
                    c.name.toLowerCase() === this.characterForm.name.toLowerCase()
                );
                if (existingCharacter) {
                    this.showNotification('角色名称已存在', 'warning');
                    return;
                }
            }

            try {
                const basePath = this.currentProject.path;
                const characterId = this.editingCharacter ?
                    this.editingCharacter.id :
                    this.generateSafeFileName(this.characterForm.name);

                const characterData = {
                    name: this.characterForm.name,
                    archetype: this.characterForm.archetype,
                    motivation: this.characterForm.motivation,
                    appearance: this.characterForm.appearance,
                    knowledge: [],
                    beliefs: [],
                    secrets: [],
                    state: {
                        emotion: this.characterForm.emotion || 'Neutral',
                        location: this.characterForm.location || '',
                        inventory: []
                    },
                    relationships: {}
                };

                const filePath = `${basePath}/1_knowledge_base/characters/${characterId}.yml`;
                const result = await window.electronAPI.writeYaml(filePath, characterData);

                if (result.success) {
                    await this.loadCharacters();
                    await this.updateProjectStats();
                    this.closeCharacterDialog();
                } else {
                    alert('保存角色失败: ' + result.error);
                }
            } catch (error) {
                console.error('保存角色失败:', error);
                alert('保存角色失败: ' + error.message);
            }
        },

        async deleteCharacter(character) {
            if (!confirm(`确定要删除角色 "${character.name}" 吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const basePath = this.currentProject.path;
                const filePath = `${basePath}/1_knowledge_base/characters/${character.filename}`;

                const result = await window.electronAPI.deleteFile(filePath);

                if (result.success) {
                    await this.loadCharacters();
                    await this.updateProjectStats();
                } else {
                    alert('删除角色失败: ' + result.error);
                }
            } catch (error) {
                console.error('删除角色失败:', error);
                alert('删除角色失败: ' + error.message);
            }
        },

        // 地点管理方法
        showCreateLocationDialog() {
            this.editingLocation = null;
            this.locationForm = {
                name: '',
                type: '',
                description: '',
                atmosphere: ''
            };
            this.showLocationDialog = true;
        },

        editLocation(location) {
            this.editingLocation = location;
            this.locationForm = {
                name: location.name || '',
                type: location.type || '',
                description: location.description || '',
                atmosphere: location.atmosphere || ''
            };
            this.showLocationDialog = true;
        },

        closeLocationDialog() {
            this.showLocationDialog = false;
            this.editingLocation = null;
        },

        async saveLocation() {
            if (!this.locationForm.name.trim()) {
                alert('请输入地点名称');
                return;
            }

            try {
                const basePath = this.currentProject.path;
                const locationId = this.editingLocation ?
                    this.editingLocation.id :
                    this.generateSafeFileName(this.locationForm.name);

                const locationData = {
                    name: this.locationForm.name,
                    type: this.locationForm.type,
                    description: this.locationForm.description,
                    atmosphere: this.locationForm.atmosphere,
                    features: [],
                    connections: []
                };

                const filePath = `${basePath}/1_knowledge_base/locations/${locationId}.yml`;
                const result = await window.electronAPI.writeYaml(filePath, locationData);

                if (result.success) {
                    await this.loadLocations();
                    await this.updateProjectStats();
                    this.closeLocationDialog();
                } else {
                    alert('保存地点失败: ' + result.error);
                }
            } catch (error) {
                console.error('保存地点失败:', error);
                alert('保存地点失败: ' + error.message);
            }
        },

        async deleteLocation(location) {
            if (!confirm(`确定要删除地点 "${location.name}" 吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const basePath = this.currentProject.path;
                const filePath = `${basePath}/1_knowledge_base/locations/${location.filename}`;

                const result = await window.electronAPI.deleteFile(filePath);

                if (result.success) {
                    await this.loadLocations();
                    await this.updateProjectStats();
                } else {
                    alert('删除地点失败: ' + result.error);
                }
            } catch (error) {
                console.error('删除地点失败:', error);
                alert('删除地点失败: ' + error.message);
            }
        },

        // 组织管理方法
        showCreateOrganizationDialog() {
            this.editingOrganization = null;
            this.organizationForm = {
                name: '',
                type: '',
                description: '',
                leader: '',
                members: [],
                influence_level: '',
                status: '',
                headquarters: '',
                resources: ''
            };
            this.showOrganizationDialog = true;
        },

        editOrganization(organization) {
            this.editingOrganization = organization;
            this.organizationForm = {
                name: organization.name || '',
                type: organization.type || '',
                description: organization.description || '',
                leader: organization.leader || '',
                members: Array.isArray(organization.members) ? [...organization.members] : [],
                influence_level: organization.influence_level || '',
                status: organization.status || '',
                headquarters: organization.headquarters || '',
                resources: organization.resources || ''
            };
            this.showOrganizationDialog = true;
        },

        closeOrganizationDialog() {
            this.showOrganizationDialog = false;
            this.editingOrganization = null;
        },

        async saveOrganization() {
            if (!this.organizationForm.name.trim()) {
                this.showNotification('请输入组织名称', 'warning');
                return;
            }

            // 验证组织名称是否已存在（编辑模式除外）
            if (!this.editingOrganization) {
                const existingOrganization = this.organizations.find(o =>
                    o.name.toLowerCase() === this.organizationForm.name.toLowerCase()
                );
                if (existingOrganization) {
                    this.showNotification('组织名称已存在', 'warning');
                    return;
                }
            }

            try {
                const basePath = this.currentProject.path;
                const organizationId = this.editingOrganization ?
                    this.editingOrganization.id :
                    this.generateSafeFileName(this.organizationForm.name);

                const organizationData = {
                    name: this.organizationForm.name,
                    type: this.organizationForm.type,
                    description: this.organizationForm.description,
                    leader: this.organizationForm.leader,
                    members: this.organizationForm.members,
                    influence_level: this.organizationForm.influence_level,
                    status: this.organizationForm.status,
                    headquarters: this.organizationForm.headquarters,
                    resources: this.organizationForm.resources,
                    relationships: {},
                    goals: [],
                    history: []
                };

                const filePath = `${basePath}/1_knowledge_base/organizations/${organizationId}.yml`;
                const result = await window.electronAPI.writeYaml(filePath, organizationData);

                if (result.success) {
                    await this.loadOrganizations();
                    await this.updateProjectStats();
                    this.closeOrganizationDialog();
                    this.showNotification('组织保存成功', 'success');
                } else {
                    this.showNotification('保存组织失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('保存组织失败:', error);
                this.showNotification('保存组织失败: ' + error.message, 'error');
            }
        },

        async deleteOrganization(organization) {
            if (!confirm(`确定要删除组织 "${organization.name}" 吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const basePath = this.currentProject.path;
                const filePath = `${basePath}/1_knowledge_base/organizations/${organization.filename}`;

                const result = await window.electronAPI.deleteFile(filePath);

                if (result.success) {
                    await this.loadOrganizations();
                    await this.updateProjectStats();
                    this.showNotification('组织删除成功', 'success');
                } else {
                    this.showNotification('删除组织失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('删除组织失败:', error);
                this.showNotification('删除组织失败: ' + error.message, 'error');
            }
        },

        // 获取影响力等级文本
        getInfluenceLevelText(level) {
            const levels = {
                local: '地方性',
                regional: '区域性',
                national: '国家级',
                international: '国际性',
                legendary: '传奇级'
            };
            return levels[level] || '未知';
        },

        // 获取组织状态文本
        getOrganizationStatusText(status) {
            const statuses = {
                active: '活跃',
                dormant: '休眠',
                declining: '衰落',
                rising: '崛起',
                disbanded: '解散',
                hidden: '隐秘'
            };
            return statuses[status] || '未知';
        },

        // 场景管理方法
        showCreateSceneDialog() {
            this.editingScene = null;
            this.sceneForm = {
                scene_id: '',
                storyline: 'main',
                characters: [],
                location: '',
                goals: [{ type: 'exposition', content: '' }],
                description: ''
            };
            this.showSceneDialog = true;
        },

        editScene(scene) {
            this.editingScene = scene;
            this.sceneForm = {
                scene_id: scene.scene_id || '',
                storyline: scene.storyline || 'main',
                characters: Array.isArray(scene.characters) ? [...scene.characters] : [],
                location: scene.location || '',
                goals: Array.isArray(scene.goal) ? [...scene.goal] : [{ type: 'exposition', content: '' }],
                description: scene.description || ''
            };
            this.showSceneDialog = true;
        },

        closeSceneDialog() {
            this.showSceneDialog = false;
            this.editingScene = null;
        },

        addGoal() {
            this.sceneForm.goals.push({ type: 'exposition', content: '' });
        },

        removeGoal(index) {
            this.sceneForm.goals.splice(index, 1);
        },

        async saveScene() {
            if (!this.sceneForm.scene_id.trim()) {
                alert('请输入场景ID');
                return;
            }

            try {
                const basePath = this.currentProject.path;
                const sceneId = this.sceneForm.scene_id;

                // 构建场景数据
                const sceneData = {
                    scene_id: this.sceneForm.scene_id,
                    storyline: this.sceneForm.storyline,
                    characters: this.sceneForm.characters,
                    location: this.sceneForm.location,
                    goal: this.sceneForm.goals.filter(g => g.content.trim())
                };

                // 构建Markdown内容
                let yamlContent = '';
                yamlContent += `scene_id: ${sceneData.scene_id}\n`;
                yamlContent += `storyline: ${sceneData.storyline}\n`;

                if (sceneData.characters.length > 0) {
                    yamlContent += 'characters:\n';
                    sceneData.characters.forEach(char => {
                        yamlContent += `  - ${char}\n`;
                    });
                }

                if (sceneData.location) {
                    yamlContent += `location: ${sceneData.location}\n`;
                }

                if (sceneData.goal.length > 0) {
                    yamlContent += 'goal:\n';
                    sceneData.goal.forEach(goal => {
                        yamlContent += `  - type: ${goal.type}\n`;
                        yamlContent += `    content: "${goal.content}"\n`;
                    });
                }

                const markdownContent = `---\n${yamlContent}---\n\n# 场景简述\n${this.sceneForm.description}`;

                const filePath = `${basePath}/2_plot/scene_cards/${sceneId}.md`;
                const result = await window.electronAPI.writeFile(filePath, markdownContent);

                if (result.success) {
                    await this.loadScenes();
                    await this.updateProjectStats();
                    this.closeSceneDialog();
                } else {
                    alert('保存场景失败: ' + result.error);
                }
            } catch (error) {
                console.error('保存场景失败:', error);
                alert('保存场景失败: ' + error.message);
            }
        },

        async deleteScene(scene) {
            if (!confirm(`确定要删除场景 "${scene.scene_id}" 吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const basePath = this.currentProject.path;
                const filePath = `${basePath}/2_plot/scene_cards/${scene.filename}`;

                const result = await window.electronAPI.deleteFile(filePath);

                if (result.success) {
                    await this.loadScenes();
                    await this.updateProjectStats();
                } else {
                    alert('删除场景失败: ' + result.error);
                }
            } catch (error) {
                console.error('删除场景失败:', error);
                alert('删除场景失败: ' + error.message);
            }
        },

        async generateDraft(scene) {
            if (scene.hasDraft) {
                this.showNotification('该场景已有草稿', 'warning');
                return;
            }

            if (!this.apiConfigured) {
                this.showNotification('请先配置AI API设置', 'warning');
                this.showApiConfigDialog();
                return;
            }

            try {
                this.isGenerating = true;
                this.generatingText = 'AI正在生成初稿...';
                this.generatingSubtext = '正在分析场景信息和上下文，这可能需要几十秒时间';

                // 创建一个纯净的场景数据对象，避免Vue响应式代理
                const cleanSceneData = {
                    scene_id: scene.scene_id,
                    storyline: scene.storyline,
                    characters: Array.isArray(scene.characters) ? [...scene.characters] : [],
                    location: scene.location,
                    goal: Array.isArray(scene.goal) ? scene.goal.map(g => ({
                        type: g.type,
                        content: g.content
                    })) : [],
                    description: scene.description
                };

                const result = await window.electronAPI.generateSceneDraft(
                    this.currentProject.path,
                    cleanSceneData
                );

                if (result.success) {
                    // 重新加载场景数据以更新hasDraft状态
                    await this.loadScenes();
                    await this.loadDrafts();
                    await this.updateProjectStats();
                    this.showNotification('初稿生成成功！请在草稿箱中查看。', 'success');
                } else {
                    this.showNotification('生成失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('生成初稿失败:', error);
                this.showNotification('生成失败: ' + error.message, 'error');
            } finally {
                this.isGenerating = false;
            }
        },

        // API配置相关方法
        checkApiConfiguration() {
            if (this.currentProject && this.currentProject.config) {
                const apiSettings = this.currentProject.config.api_settings;
                this.apiConfigured = !!(apiSettings && apiSettings.api_key);

                if (this.apiConfigured) {
                    this.apiForm = {
                        base_url: apiSettings.base_url || '',
                        api_key: apiSettings.api_key,
                        model: apiSettings.model || 'gpt-3.5-turbo',
                        temperature: apiSettings.temperature || 0.7
                    };
                }
            }
        },

        showApiConfigDialog() {
            this.showApiDialog = true;
        },

        closeApiDialog() {
            this.showApiDialog = false;
        },

        async saveApiConfig() {
            if (!this.apiForm.api_key.trim()) {
                this.showNotification('请输入API Key', 'warning');
                return;
            }

            try {
                // 更新项目配置
                const config = { ...this.currentProject.config };
                config.api_settings = {
                    base_url: this.apiForm.base_url.trim() || 'https://api.openai.com/v1',
                    api_key: this.apiForm.api_key,
                    model: this.apiForm.model || 'gpt-3.5-turbo',
                    temperature: parseFloat(this.apiForm.temperature)
                };

                const configPath = `${this.currentProject.path}/chronicler.yml`;
                const result = await window.electronAPI.writeYaml(configPath, config);

                if (result.success) {
                    this.currentProject.config = config;
                    this.apiConfigured = true;
                    this.closeApiDialog();
                    this.showNotification('API配置保存成功！', 'success');
                } else {
                    this.showNotification('保存配置失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('保存API配置失败:', error);
                this.showNotification('保存配置失败: ' + error.message, 'error');
            }
        },

        // 设置模型
        setModel(modelName) {
            this.apiForm.model = modelName;
        },

        // 生成安全的文件名
        generateSafeFileName(name) {
            if (!name || !name.trim()) {
                return 'unnamed_' + Date.now();
            }

            // 移除特殊字符，保留中文、英文、数字
            let safeName = name.trim()
                .replace(/[<>:"/\\|?*]/g, '') // 移除Windows不允许的字符
                .replace(/\s+/g, '_') // 空格替换为下划线
                .replace(/[^\u4e00-\u9fa5a-zA-Z0-9_-]/g, '') // 只保留中文、英文、数字、下划线、连字符
                .substring(0, 50); // 限制长度

            // 如果处理后为空，使用时间戳
            if (!safeName) {
                safeName = 'item_' + Date.now();
            }

            return safeName;
        },

        // 世界观设定相关方法
        async loadWorldBible() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;
                const worldBiblePath = `${basePath}/1_knowledge_base/world_bible.md`;

                const result = await window.electronAPI.readFile(worldBiblePath);
                if (result.success) {
                    this.worldBibleContent = result.data;
                    this.worldBibleChanged = false;
                } else {
                    this.worldBibleContent = '';
                    this.worldBibleChanged = false;
                }
            } catch (error) {
                console.error('加载世界观设定失败:', error);
                this.worldBibleContent = '';
                this.worldBibleChanged = false;
            }
        },

        onWorldBibleInput() {
            this.worldBibleChanged = true;

            // 自动保存（5秒后）
            if (this.worldBibleAutoSaveTimer) {
                clearTimeout(this.worldBibleAutoSaveTimer);
            }

            this.worldBibleAutoSaveTimer = setTimeout(() => {
                this.saveWorldBible();
            }, 5000);
        },

        async saveWorldBible() {
            if (!this.currentProject || !this.worldBibleChanged) return;

            try {
                const basePath = this.currentProject.path;
                const worldBiblePath = `${basePath}/1_knowledge_base/world_bible.md`;

                const result = await window.electronAPI.writeFile(worldBiblePath, this.worldBibleContent);

                if (result.success) {
                    this.worldBibleChanged = false;
                    this.showNotification('世界观设定已保存', 'success', 2000);
                } else {
                    this.showNotification('保存失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('保存世界观设定失败:', error);
                this.showNotification('保存失败: ' + error.message, 'error');
            }
        },

        insertWorldSection(sectionName) {
            const editor = this.$refs.worldBibleEditor;
            if (!editor) return;

            const cursorPos = editor.selectionStart;
            let insertText = '';

            switch (sectionName) {
                case '基本设定':
                    insertText = `\n\n## 基本设定\n\n### 时代背景\n- 时间：\n- 地点：\n- 社会制度：\n\n### 物理法则\n- 魔法系统：\n- 科技水平：\n- 特殊规则：\n\n`;
                    break;
                case '历史背景':
                    insertText = `\n\n## 历史背景\n\n### 重要事件\n- \n\n### 传说故事\n- \n\n### 历史人物\n- \n\n`;
                    break;
                case '文化设定':
                    insertText = `\n\n## 文化设定\n\n### 语言文字\n- \n\n### 宗教信仰\n- \n\n### 社会习俗\n- \n\n`;
                    break;
                case '地理环境':
                    insertText = `\n\n## 地理环境\n\n### 主要地区\n- \n\n### 气候特征\n- \n\n### 自然资源\n- \n\n`;
                    break;
                case '魔法系统':
                    insertText = `\n\n## 魔法系统\n\n### 魔法原理\n- \n\n### 魔法分类\n- \n\n### 使用限制\n- \n\n`;
                    break;
            }

            this.worldBibleContent = this.worldBibleContent.substring(0, cursorPos) + insertText + this.worldBibleContent.substring(cursorPos);
            this.onWorldBibleInput();

            // 设置光标位置
            this.$nextTick(() => {
                editor.focus();
                editor.setSelectionRange(cursorPos + insertText.length, cursorPos + insertText.length);
            });
        },

        // 主线大纲相关方法
        async loadOutline() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;
                const outlinePath = `${basePath}/2_plot/main_outline.md`;

                const result = await window.electronAPI.readFile(outlinePath);
                if (result.success) {
                    this.outlineContent = result.data;
                    this.outlineChanged = false;
                } else {
                    this.outlineContent = '';
                    this.outlineChanged = false;
                }
            } catch (error) {
                console.error('加载主线大纲失败:', error);
                this.outlineContent = '';
                this.outlineChanged = false;
            }
        },

        onOutlineInput() {
            this.outlineChanged = true;

            // 自动保存（5秒后）
            if (this.outlineAutoSaveTimer) {
                clearTimeout(this.outlineAutoSaveTimer);
            }

            this.outlineAutoSaveTimer = setTimeout(() => {
                this.saveOutline();
            }, 5000);
        },

        async saveOutline() {
            if (!this.currentProject || !this.outlineChanged) return;

            try {
                const basePath = this.currentProject.path;
                const outlinePath = `${basePath}/2_plot/main_outline.md`;

                const result = await window.electronAPI.writeFile(outlinePath, this.outlineContent);

                if (result.success) {
                    this.outlineChanged = false;
                    this.showNotification('主线大纲已保存', 'success', 2000);
                } else {
                    this.showNotification('保存失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('保存主线大纲失败:', error);
                this.showNotification('保存失败: ' + error.message, 'error');
            }
        },

        insertOutlineSection(sectionName) {
            const editor = this.$refs.outlineEditor;
            if (!editor) return;

            const cursorPos = editor.selectionStart;
            let insertText = '';

            switch (sectionName) {
                case '故事概述':
                    insertText = `\n\n## 故事概述\n\n### 核心冲突\n- \n\n### 主要情节线\n- \n\n### 故事主题\n- \n\n`;
                    break;
                case '主要角色':
                    insertText = `\n\n## 主要角色\n\n### 主角\n- 姓名：\n- 性格：\n- 目标：\n\n### 配角\n- \n\n### 反派\n- \n\n`;
                    break;
                case '情节结构':
                    insertText = `\n\n## 情节结构\n\n### 第一幕：开端\n- \n\n### 第二幕：发展\n- \n\n### 第三幕：高潮\n- \n\n### 第四幕：结局\n- \n\n`;
                    break;
                case '主题思想':
                    insertText = `\n\n## 主题思想\n\n### 核心主题\n- \n\n### 价值观念\n- \n\n### 寓意表达\n- \n\n`;
                    break;
                case '分章计划':
                    insertText = `\n\n## 分章计划\n\n### 第一章\n- 标题：\n- 内容：\n- 目标：\n\n### 第二章\n- 标题：\n- 内容：\n- 目标：\n\n`;
                    break;
            }

            this.outlineContent = this.outlineContent.substring(0, cursorPos) + insertText + this.outlineContent.substring(cursorPos);
            this.onOutlineInput();

            // 设置光标位置
            this.$nextTick(() => {
                editor.focus();
                editor.setSelectionRange(cursorPos + insertText.length, cursorPos + insertText.length);
            });
        },

        // 文本编辑器相关方法
        editDraft(draft) {
            this.editingDraft = draft;
            this.editorContent = draft.content;
            this.isSaved = true;
            this.showEditorDialog = true;

            // 聚焦到编辑器
            this.$nextTick(() => {
                if (this.$refs.editor) {
                    this.$refs.editor.focus();
                }
            });
        },

        closeEditorDialog() {
            if (!this.isSaved) {
                if (!confirm('有未保存的更改，确定要关闭吗？')) {
                    return;
                }
            }

            this.showEditorDialog = false;
            this.editingDraft = null;
            this.editorContent = '';
            this.isSaved = true;

            if (this.autoSaveTimer) {
                clearTimeout(this.autoSaveTimer);
                this.autoSaveTimer = null;
            }
        },

        onEditorInput() {
            this.isSaved = false;

            // 自动保存（3秒后）
            if (this.autoSaveTimer) {
                clearTimeout(this.autoSaveTimer);
            }

            this.autoSaveTimer = setTimeout(() => {
                this.saveDraft();
            }, 3000);
        },

        onEditorKeydown(event) {
            // Ctrl+S 保存
            if (event.ctrlKey && event.key === 's') {
                event.preventDefault();
                this.saveDraft();
            }
        },

        async saveDraft() {
            if (!this.editingDraft) return;

            try {
                const basePath = this.currentProject.path;
                const filePath = `${basePath}/3_manuscript/drafts/${this.editingDraft.filename}`;

                const result = await window.electronAPI.writeFile(filePath, this.editorContent);

                if (result.success) {
                    this.isSaved = true;
                    // 更新草稿内容
                    this.editingDraft.content = this.editorContent;
                    this.editingDraft.preview = this.editorContent.substring(0, 200).replace(/[#>\n]/g, ' ').trim() + '...';
                } else {
                    alert('保存失败: ' + result.error);
                }
            } catch (error) {
                console.error('保存草稿失败:', error);
                alert('保存失败: ' + error.message);
            }
        },

        async publishDraft() {
            if (!this.editingDraft || !this.editorContent.trim()) {
                this.showNotification('内容不能为空', 'warning');
                return;
            }

            if (!this.apiConfigured) {
                this.showNotification('请先配置AI API设置以进行状态分析', 'warning');
                this.showApiConfigDialog();
                return;
            }

            if (!confirm('确定要提交这个草稿吗？提交后将进行AI分析并自动更新角色状态。')) {
                return;
            }

            try {
                this.isGenerating = true;
                this.generatingText = 'AI正在分析场景...';
                this.generatingSubtext = '正在提取状态变化并更新角色信息';

                // 先保存当前内容
                await this.saveDraft();

                // 提取场景ID（从文件名中）
                const sceneId = this.editingDraft.filename.replace('.md', '');

                // 1. AI分析并生成摘要，自动更新角色状态
                const analysisResult = await window.electronAPI.analyzeAndUpdateState(
                    this.currentProject.path,
                    sceneId,
                    this.editorContent
                );

                if (!analysisResult.success) {
                    this.showNotification('AI分析失败: ' + analysisResult.error, 'error');
                    return;
                }

                // 2. 移动文件到已发布目录
                const basePath = this.currentProject.path;
                const draftPath = `${basePath}/3_manuscript/drafts/${this.editingDraft.filename}`;
                const publishedPath = `${basePath}/3_manuscript/published/${this.editingDraft.filename}`;

                // 读取草稿内容并写入到已发布目录
                const result = await window.electronAPI.writeFile(publishedPath, this.editorContent);

                if (result.success) {
                    // 3. 删除草稿文件
                    await window.electronAPI.deleteFile(draftPath);

                    // 4. 重新加载所有数据以反映状态变化
                    await this.loadDrafts();
                    await this.loadPublishedChapters();
                    await this.loadCharacters(); // 重新加载角色以显示状态更新
                    await this.loadScenes(); // 重新加载场景
                    await this.updateProjectStats();

                    this.closeEditorDialog();

                    // 5. 显示分析结果
                    this.showAnalysisResult(analysisResult.summary);

                } else {
                    this.showNotification('提交失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('提交草稿失败:', error);
                this.showNotification('提交失败: ' + error.message, 'error');
            } finally {
                this.isGenerating = false;
            }
        },

        // 显示AI分析结果
        showAnalysisResult(summary) {
            // 首先显示成功通知
            this.showNotification('章节提交成功！AI分析已完成，角色状态已更新。', 'success', 3000);

            // 然后显示详细的分析结果
            setTimeout(() => {
                let message = '📊 AI分析结果：\n\n';

                // 显示角色状态更新
                if (summary.state_updates && Object.keys(summary.state_updates).length > 0) {
                    message += '角色状态更新：\n';
                    Object.entries(summary.state_updates).forEach(([charId, updates]) => {
                        message += `• ${charId}:\n`;
                        if (updates.emotion) message += `  - 情绪: ${updates.emotion}\n`;
                        if (updates.location) message += `  - 位置: ${updates.location}\n`;
                        if (updates.inventory_add && updates.inventory_add.length > 0) {
                            message += `  - 新增物品: ${updates.inventory_add.join(', ')}\n`;
                        }
                    });
                    message += '\n';
                }

                // 显示关系更新
                if (summary.relationship_updates && Object.keys(summary.relationship_updates).length > 0) {
                    message += '关系变化：\n';
                    Object.entries(summary.relationship_updates).forEach(([charId, relationships]) => {
                        Object.entries(relationships).forEach(([otherChar, rel]) => {
                            message += `• ${charId} ↔ ${otherChar}: ${rel.status}\n`;
                        });
                    });
                    message += '\n';
                }

                // 显示新信息
                if (summary.new_information_revealed && summary.new_information_revealed.length > 0) {
                    message += '新揭露信息：\n';
                    summary.new_information_revealed.forEach(info => {
                        message += `• ${info}\n`;
                    });
                    message += '\n';
                }

                // 显示下一场景建议
                if (summary.next_scene_recommendation) {
                    message += `下一场景建议：\n${summary.next_scene_recommendation}`;
                }

                // 如果有实际的更新内容，才显示详细信息
                if (message.length > 20) {
                    alert(message);
                }
            }, 3500);
        },

        async deleteDraft(draft) {
            if (!confirm(`确定要删除草稿 "${draft.title}" 吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const basePath = this.currentProject.path;
                const filePath = `${basePath}/3_manuscript/drafts/${draft.filename}`;

                const result = await window.electronAPI.deleteFile(filePath);

                if (result.success) {
                    await this.loadDrafts();
                    await this.updateProjectStats();
                } else {
                    alert('删除失败: ' + result.error);
                }
            } catch (error) {
                console.error('删除草稿失败:', error);
                alert('删除失败: ' + error.message);
            }
        },

        viewChapter(chapter) {
            // 简单的查看功能，在新窗口中显示内容
            const newWindow = window.open('', '_blank');
            newWindow.document.write(`
                <html>
                <head>
                    <title>${chapter.title}</title>
                    <style>
                        body { font-family: Georgia, serif; line-height: 1.8; max-width: 800px; margin: 0 auto; padding: 20px; }
                        h1 { color: #2c3e50; }
                    </style>
                </head>
                <body>
                    <h1>${chapter.title}</h1>
                    <pre style="white-space: pre-wrap; font-family: Georgia, serif;">${chapter.content}</pre>
                </body>
                </html>
            `);
        },

        // 编辑器工具栏功能
        formatText(format) {
            const editor = this.$refs.editor;
            if (!editor) return;

            const start = editor.selectionStart;
            const end = editor.selectionEnd;
            const selectedText = this.editorContent.substring(start, end);

            if (!selectedText) {
                alert('请先选择要格式化的文本');
                return;
            }

            let formattedText = selectedText;
            switch (format) {
                case 'bold':
                    formattedText = `**${selectedText}**`;
                    break;
                case 'italic':
                    formattedText = `*${selectedText}*`;
                    break;
                case 'underline':
                    formattedText = `<u>${selectedText}</u>`;
                    break;
            }

            this.editorContent = this.editorContent.substring(0, start) + formattedText + this.editorContent.substring(end);
            this.onEditorInput();

            // 重新设置光标位置
            this.$nextTick(() => {
                editor.focus();
                editor.setSelectionRange(start, start + formattedText.length);
            });
        },

        insertText(type) {
            const editor = this.$refs.editor;
            if (!editor) return;

            const cursorPos = editor.selectionStart;
            let insertText = '';

            switch (type) {
                case 'dialogue':
                    insertText = '\n\n"对话内容，"角色说道。\n\n';
                    break;
                case 'description':
                    insertText = '\n\n[在这里添加环境或动作描述]\n\n';
                    break;
                case 'action':
                    insertText = '\n\n角色进行了某个动作。\n\n';
                    break;
            }

            this.editorContent = this.editorContent.substring(0, cursorPos) + insertText + this.editorContent.substring(cursorPos);
            this.onEditorInput();

            // 设置光标位置
            this.$nextTick(() => {
                editor.focus();
                editor.setSelectionRange(cursorPos + insertText.length, cursorPos + insertText.length);
            });
        }
    },
    
    mounted() {
        // 监听菜单事件
        window.electronAPI.onMenuNewProject(() => {
            this.createNewProject();
        });

        window.electronAPI.onMenuOpenProject(() => {
            this.openExistingProject();
        });

        // 监听窗口关闭事件，清理定时器
        window.addEventListener('beforeunload', () => {
            if (this.autoSaveTimer) {
                clearTimeout(this.autoSaveTimer);
            }
            if (this.notification.timer) {
                clearTimeout(this.notification.timer);
            }
            if (this.worldBibleAutoSaveTimer) {
                clearTimeout(this.worldBibleAutoSaveTimer);
            }
            if (this.outlineAutoSaveTimer) {
                clearTimeout(this.outlineAutoSaveTimer);
            }
        });

        // 监听键盘快捷键
        window.addEventListener('keydown', (event) => {
            // Ctrl+N 新建项目
            if (event.ctrlKey && event.key === 'n') {
                event.preventDefault();
                this.createNewProject();
            }
            // Ctrl+O 打开项目
            if (event.ctrlKey && event.key === 'o') {
                event.preventDefault();
                this.openExistingProject();
            }
            // Esc 关闭对话框
            if (event.key === 'Escape') {
                if (this.showCharacterDialog) this.closeCharacterDialog();
                if (this.showLocationDialog) this.closeLocationDialog();
                if (this.showOrganizationDialog) this.closeOrganizationDialog();
                if (this.showSceneDialog) this.closeSceneDialog();
                if (this.showApiDialog) this.closeApiDialog();
                if (this.showEditorDialog) this.closeEditorDialog();
                if (this.notification.show) this.hideNotification();
            }
        });
    },
    
    beforeUnmount() {
        // 清理事件监听器
        window.electronAPI.removeAllListeners('menu-new-project');
        window.electronAPI.removeAllListeners('menu-open-project');
    }
}).mount('#app');
