const { contextBridge, ipc<PERSON>ender<PERSON> } = require('electron');

// 暴露受保护的方法给渲染进程，在window.electronAPI下
contextBridge.exposeInMainWorld('electronAPI', {
  // 文件系统操作
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  writeFile: (filePath, data) => ipcRenderer.invoke('write-file', filePath, data),
  readYaml: (filePath) => ipcRenderer.invoke('read-yaml', filePath),
  writeYaml: (filePath, data) => ipcRenderer.invoke('write-yaml', filePath, data),
  createDirectory: (dirPath) => ipcRenderer.invoke('create-directory', dirPath),
  checkPathExists: (path) => ipcRenderer.invoke('check-path-exists', path),
  listDirectory: (dirPath) => ipcRenderer.invoke('list-directory', dirPath),
  deleteFile: (filePath) => ipcRenderer.invoke('delete-file', filePath),

  // AI生成功能
  generateSceneDraft: (projectPath, sceneData) => ipcRenderer.invoke('generate-scene-draft', projectPath, sceneData),
  analyzeAndUpdateState: (projectPath, sceneId, content) => ipcRenderer.invoke('analyze-and-update-state', projectPath, sceneId, content),
  aiCreateScene: (projectPath) => ipcRenderer.invoke('ai-create-scene', projectPath),

  // 菜单事件监听
  onMenuNewProject: (callback) => ipcRenderer.on('menu-new-project', callback),
  onMenuOpenProject: (callback) => ipcRenderer.on('menu-open-project', callback),

  // 移除监听器
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});
