<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chronicler - AI小说自动化生成系统</title>
    <link rel="stylesheet" href="styles/main.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
    <div id="app">
        <!-- 顶部标题栏 -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="app-icon">📚</span>
                    Chronicler
                </h1>
                <div class="project-info" v-if="currentProject">
                    <span class="project-name">{{ currentProject.name }}</span>
                    <span class="project-path">{{ currentProject.path }}</span>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <div class="app-main">
            <!-- 左侧导航栏 -->
            <nav class="sidebar">
                <div class="nav-section">
                    <h3 class="nav-title">项目管理</h3>
                    <ul class="nav-list">
                        <li class="nav-item" :class="{ active: activeView === 'dashboard' }" @click="setActiveView('dashboard')">
                            <span class="nav-icon">🏠</span>
                            项目概览
                        </li>
                    </ul>
                </div>

                <div class="nav-section" v-if="currentProject">
                    <h3 class="nav-title">知识库</h3>
                    <ul class="nav-list">
                        <li class="nav-item" :class="{ active: activeView === 'characters' }" @click="setActiveView('characters')">
                            <span class="nav-icon">👥</span>
                            角色管理
                        </li>
                        <li class="nav-item" :class="{ active: activeView === 'locations' }" @click="setActiveView('locations')">
                            <span class="nav-icon">🏛️</span>
                            地点管理
                        </li>
                        <li class="nav-item" :class="{ active: activeView === 'organizations' }" @click="setActiveView('organizations')">
                            <span class="nav-icon">🏢</span>
                            组织管理
                        </li>
                        <li class="nav-item" :class="{ active: activeView === 'worldbible' }" @click="setActiveView('worldbible')">
                            <span class="nav-icon">🌍</span>
                            世界观设定
                        </li>
                    </ul>
                </div>

                <div class="nav-section" v-if="currentProject">
                    <h3 class="nav-title">情节规划</h3>
                    <ul class="nav-list">
                        <li class="nav-item" :class="{ active: activeView === 'outline' }" @click="setActiveView('outline')">
                            <span class="nav-icon">📋</span>
                            主线大纲
                        </li>
                        <li class="nav-item" :class="{ active: activeView === 'scenes' }" @click="setActiveView('scenes')">
                            <span class="nav-icon">🎬</span>
                            场景卡片
                        </li>
                    </ul>
                </div>

                <div class="nav-section" v-if="currentProject">
                    <h3 class="nav-title">创作工坊</h3>
                    <ul class="nav-list">
                        <li class="nav-item" :class="{ active: activeView === 'drafts' }" @click="setActiveView('drafts')">
                            <span class="nav-icon">📝</span>
                            草稿箱
                        </li>
                        <li class="nav-item" :class="{ active: activeView === 'published' }" @click="setActiveView('published')">
                            <span class="nav-icon">📖</span>
                            已发布
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="content-area">
                <!-- 项目概览视图 -->
                <div v-if="activeView === 'dashboard'" class="view-container">
                    <div class="view-header">
                        <h2>项目概览</h2>
                    </div>
                    <div class="dashboard-content">
                        <div v-if="!currentProject" class="welcome-section">
                            <div class="welcome-card">
                                <h3>欢迎使用 Chronicler</h3>
                                <p>开始您的AI辅助小说创作之旅</p>
                                <div class="action-buttons">
                                    <button class="btn btn-primary" @click="createNewProject">
                                        <span class="btn-icon">➕</span>
                                        新建项目
                                    </button>
                                    <button class="btn btn-secondary" @click="openExistingProject">
                                        <span class="btn-icon">📂</span>
                                        打开项目
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div v-else class="project-dashboard">
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-number">{{ projectStats.characters }}</div>
                                    <div class="stat-label">角色</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">{{ projectStats.locations }}</div>
                                    <div class="stat-label">地点</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">{{ projectStats.organizations }}</div>
                                    <div class="stat-label">组织</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">{{ projectStats.scenes }}</div>
                                    <div class="stat-label">场景</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">{{ projectStats.drafts }}</div>
                                    <div class="stat-label">草稿</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">{{ projectStats.published }}</div>
                                    <div class="stat-label">已发布</div>
                                </div>
                            </div>

                            <!-- API配置区域 -->
                            <div class="api-config-section">
                                <h3>AI配置</h3>
                                <div class="config-status">
                                    <span v-if="apiConfigured" class="status-indicator status-ok">✓ API已配置</span>
                                    <span v-else class="status-indicator status-warning">⚠ 需要配置API</span>
                                    <button class="btn btn-secondary btn-small" @click="showApiConfigDialog">
                                        {{ apiConfigured ? '修改配置' : '配置API' }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 角色管理视图 -->
                <div v-else-if="activeView === 'characters'" class="view-container">
                    <div class="view-header">
                        <h2>角色管理</h2>
                        <button class="btn btn-primary" @click="showCreateCharacterDialog">
                            <span class="btn-icon">➕</span>
                            新建角色
                        </button>
                    </div>
                    <div class="view-content">
                        <div v-if="characters.length === 0" class="empty-state">
                            <p>还没有创建任何角色</p>
                            <button class="btn btn-primary" @click="showCreateCharacterDialog">创建第一个角色</button>
                        </div>
                        <div v-else class="characters-grid">
                            <div v-for="character in characters" :key="character.id" class="character-card">
                                <div class="character-header">
                                    <h3 class="character-name">{{ character.name }}</h3>
                                    <div class="character-actions">
                                        <button class="btn-icon-small" @click="editCharacter(character)" title="编辑">✏️</button>
                                        <button class="btn-icon-small" @click="deleteCharacter(character)" title="删除">🗑️</button>
                                    </div>
                                </div>
                                <div class="character-info">
                                    <p class="character-archetype">{{ character.archetype || '未设定原型' }}</p>
                                    <p class="character-motivation">{{ character.motivation || '未设定动机' }}</p>
                                    <div class="character-state">
                                        <span class="state-item">情绪: {{ character.state?.emotion || '未知' }}</span>
                                        <span class="state-item">位置: {{ character.state?.location || '未知' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 地点管理视图 -->
                <div v-else-if="activeView === 'locations'" class="view-container">
                    <div class="view-header">
                        <h2>地点管理</h2>
                        <button class="btn btn-primary" @click="showCreateLocationDialog">
                            <span class="btn-icon">➕</span>
                            新建地点
                        </button>
                    </div>
                    <div class="view-content">
                        <div v-if="locations.length === 0" class="empty-state">
                            <p>还没有创建任何地点</p>
                            <button class="btn btn-primary" @click="showCreateLocationDialog">创建第一个地点</button>
                        </div>
                        <div v-else class="locations-grid">
                            <div v-for="location in locations" :key="location.id" class="location-card">
                                <div class="location-header">
                                    <h3 class="location-name">{{ location.name }}</h3>
                                    <div class="location-actions">
                                        <button class="btn-icon-small" @click="editLocation(location)" title="编辑">✏️</button>
                                        <button class="btn-icon-small" @click="deleteLocation(location)" title="删除">🗑️</button>
                                    </div>
                                </div>
                                <div class="location-info">
                                    <p class="location-description">{{ location.description || '暂无描述' }}</p>
                                    <p class="location-type">类型: {{ location.type || '未分类' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 场景卡片管理视图 -->
                <div v-else-if="activeView === 'scenes'" class="view-container">
                    <div class="view-header">
                        <h2>场景卡片</h2>
                        <button class="btn btn-primary" @click="showCreateSceneDialog">
                            <span class="btn-icon">➕</span>
                            新建场景
                        </button>
                    </div>
                    <div class="view-content">
                        <div v-if="scenes.length === 0" class="empty-state">
                            <p>还没有创建任何场景卡片</p>
                            <button class="btn btn-primary" @click="showCreateSceneDialog">创建第一个场景</button>
                        </div>
                        <div v-else class="scenes-list">
                            <div v-for="scene in scenes" :key="scene.id" class="scene-card">
                                <div class="scene-header">
                                    <div class="scene-info">
                                        <h3 class="scene-title">{{ scene.scene_id }}</h3>
                                        <span class="scene-storyline">{{ scene.storyline || 'main' }}</span>
                                    </div>
                                    <div class="scene-actions">
                                        <button class="btn btn-small btn-success" @click="generateDraft(scene)"
                                                :disabled="scene.hasDraft" title="生成初稿">
                                            {{ scene.hasDraft ? '已生成' : '生成初稿' }}
                                        </button>
                                        <button class="btn-icon-small" @click="editScene(scene)" title="编辑">✏️</button>
                                        <button class="btn-icon-small" @click="deleteScene(scene)" title="删除">🗑️</button>
                                    </div>
                                </div>
                                <div class="scene-details">
                                    <div class="scene-participants">
                                        <strong>角色:</strong>
                                        <span v-if="scene.characters && scene.characters.length > 0">
                                            {{ scene.characters.join(', ') }}
                                        </span>
                                        <span v-else class="text-muted">未设定</span>
                                    </div>
                                    <div class="scene-location">
                                        <strong>地点:</strong>
                                        <span v-if="scene.location">{{ scene.location }}</span>
                                        <span v-else class="text-muted">未设定</span>
                                    </div>
                                    <div class="scene-goals">
                                        <strong>目标:</strong>
                                        <ul v-if="scene.goal && scene.goal.length > 0" class="goals-list">
                                            <li v-for="goal in scene.goal" :key="goal.type">
                                                <span class="goal-type">{{ goal.type }}:</span>
                                                {{ goal.content }}
                                            </li>
                                        </ul>
                                        <span v-else class="text-muted">未设定目标</span>
                                    </div>
                                    <div class="scene-description" v-if="scene.description">
                                        <strong>简述:</strong>
                                        <p>{{ scene.description }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 草稿箱视图 -->
                <div v-else-if="activeView === 'drafts'" class="view-container">
                    <div class="view-header">
                        <h2>草稿箱</h2>
                    </div>
                    <div class="view-content">
                        <div v-if="drafts.length === 0" class="empty-state">
                            <p>还没有任何草稿</p>
                            <p class="text-muted">请先在场景卡片中生成初稿</p>
                        </div>
                        <div v-else class="drafts-list">
                            <div v-for="draft in drafts" :key="draft.id" class="draft-card">
                                <div class="draft-header">
                                    <div class="draft-info">
                                        <h3 class="draft-title">{{ draft.title }}</h3>
                                        <span class="draft-meta">{{ draft.lastModified }}</span>
                                    </div>
                                    <div class="draft-actions">
                                        <button class="btn btn-primary" @click="editDraft(draft)">
                                            <span class="btn-icon">✏️</span>
                                            编辑
                                        </button>
                                        <button class="btn-icon-small" @click="deleteDraft(draft)" title="删除">🗑️</button>
                                    </div>
                                </div>
                                <div class="draft-preview">
                                    {{ draft.preview }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 已发布视图 -->
                <div v-else-if="activeView === 'published'" class="view-container">
                    <div class="view-header">
                        <h2>已发布</h2>
                    </div>
                    <div class="view-content">
                        <div v-if="publishedChapters.length === 0" class="empty-state">
                            <p>还没有发布任何章节</p>
                            <p class="text-muted">请先编辑草稿并提交终稿</p>
                        </div>
                        <div v-else class="published-list">
                            <div v-for="chapter in publishedChapters" :key="chapter.id" class="published-card">
                                <div class="published-header">
                                    <div class="published-info">
                                        <h3 class="published-title">{{ chapter.title }}</h3>
                                        <span class="published-meta">发布于 {{ chapter.publishedAt }}</span>
                                    </div>
                                    <div class="published-actions">
                                        <button class="btn btn-secondary btn-small" @click="viewChapter(chapter)">
                                            <span class="btn-icon">👁️</span>
                                            查看
                                        </button>
                                    </div>
                                </div>
                                <div class="published-preview">
                                    {{ chapter.preview }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 组织管理视图 -->
                <div v-else-if="activeView === 'organizations'" class="view-container">
                    <div class="view-header">
                        <h2>组织管理</h2>
                        <button class="btn btn-primary" @click="showCreateOrganizationDialog">
                            <span class="btn-icon">➕</span>
                            新建组织
                        </button>
                    </div>
                    <div class="view-content">
                        <div v-if="organizations.length === 0" class="empty-state">
                            <p>还没有创建任何组织</p>
                            <button class="btn btn-primary" @click="showCreateOrganizationDialog">创建第一个组织</button>
                        </div>
                        <div v-else class="organizations-grid">
                            <div v-for="organization in organizations" :key="organization.id" class="organization-card">
                                <div class="organization-header">
                                    <div class="organization-info">
                                        <h3 class="organization-name">{{ organization.name }}</h3>
                                        <span class="organization-type">{{ organization.type || '未分类' }}</span>
                                    </div>
                                    <div class="organization-actions">
                                        <button class="btn-icon-small" @click="editOrganization(organization)" title="编辑">✏️</button>
                                        <button class="btn-icon-small" @click="deleteOrganization(organization)" title="删除">🗑️</button>
                                    </div>
                                </div>
                                <div class="organization-details">
                                    <div class="organization-description">
                                        <strong>描述:</strong>
                                        <span v-if="organization.description">{{ organization.description }}</span>
                                        <span v-else class="text-muted">暂无描述</span>
                                    </div>
                                    <div class="organization-leader">
                                        <strong>领导者:</strong>
                                        <span v-if="organization.leader">{{ organization.leader }}</span>
                                        <span v-else class="text-muted">未设定</span>
                                    </div>
                                    <div class="organization-members" v-if="organization.members && organization.members.length > 0">
                                        <strong>成员:</strong>
                                        <span>{{ organization.members.join(', ') }}</span>
                                    </div>
                                    <div class="organization-influence">
                                        <strong>影响力:</strong>
                                        <span class="influence-level" :class="'influence-' + (organization.influence_level || 'unknown')">
                                            {{ getInfluenceLevelText(organization.influence_level) }}
                                        </span>
                                    </div>
                                    <div class="organization-status">
                                        <strong>状态:</strong>
                                        <span class="status-badge" :class="'status-' + (organization.status || 'unknown')">
                                            {{ getOrganizationStatusText(organization.status) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 世界观设定视图 -->
                <div v-else-if="activeView === 'worldbible'" class="view-container">
                    <div class="view-header">
                        <h2>世界观设定</h2>
                        <button class="btn btn-primary" @click="saveWorldBible" :disabled="!worldBibleChanged">
                            <span class="btn-icon">💾</span>
                            保存设定
                        </button>
                    </div>
                    <div class="view-content">
                        <div class="world-bible-editor">
                            <div class="editor-toolbar">
                                <button class="toolbar-btn" @click="insertWorldSection('基本设定')" title="插入基本设定">
                                    🌍 基本设定
                                </button>
                                <button class="toolbar-btn" @click="insertWorldSection('历史背景')" title="插入历史背景">
                                    📜 历史背景
                                </button>
                                <button class="toolbar-btn" @click="insertWorldSection('文化设定')" title="插入文化设定">
                                    🏛️ 文化设定
                                </button>
                                <button class="toolbar-btn" @click="insertWorldSection('地理环境')" title="插入地理环境">
                                    🗺️ 地理环境
                                </button>
                                <button class="toolbar-btn" @click="insertWorldSection('魔法系统')" title="插入魔法系统">
                                    ✨ 魔法系统
                                </button>
                            </div>
                            <textarea
                                ref="worldBibleEditor"
                                v-model="worldBibleContent"
                                class="world-bible-textarea"
                                placeholder="在这里编写您的世界观设定..."
                                @input="onWorldBibleInput">
                            </textarea>
                            <div class="editor-status">
                                <span class="word-count">字数: {{ worldBibleWordCount }}</span>
                                <span class="save-status" :class="{ 'saved': !worldBibleChanged, 'unsaved': worldBibleChanged }">
                                    {{ worldBibleChanged ? '未保存' : '已保存' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 主线大纲视图 -->
                <div v-else-if="activeView === 'outline'" class="view-container">
                    <div class="view-header">
                        <h2>主线大纲</h2>
                        <button class="btn btn-primary" @click="saveOutline" :disabled="!outlineChanged">
                            <span class="btn-icon">💾</span>
                            保存大纲
                        </button>
                    </div>
                    <div class="view-content">
                        <div class="outline-editor">
                            <div class="editor-toolbar">
                                <button class="toolbar-btn" @click="insertOutlineSection('故事概述')" title="插入故事概述">
                                    📖 故事概述
                                </button>
                                <button class="toolbar-btn" @click="insertOutlineSection('主要角色')" title="插入主要角色">
                                    👥 主要角色
                                </button>
                                <button class="toolbar-btn" @click="insertOutlineSection('情节结构')" title="插入情节结构">
                                    🎭 情节结构
                                </button>
                                <button class="toolbar-btn" @click="insertOutlineSection('主题思想')" title="插入主题思想">
                                    💭 主题思想
                                </button>
                                <button class="toolbar-btn" @click="insertOutlineSection('分章计划')" title="插入分章计划">
                                    📑 分章计划
                                </button>
                            </div>
                            <textarea
                                ref="outlineEditor"
                                v-model="outlineContent"
                                class="outline-textarea"
                                placeholder="在这里编写您的主线大纲..."
                                @input="onOutlineInput">
                            </textarea>
                            <div class="editor-status">
                                <span class="word-count">字数: {{ outlineWordCount }}</span>
                                <span class="save-status" :class="{ 'saved': !outlineChanged, 'unsaved': outlineChanged }">
                                    {{ outlineChanged ? '未保存' : '已保存' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 其他视图的占位符 -->
                <div v-else class="view-container">
                    <div class="view-header">
                        <h2>{{ getViewTitle() }}</h2>
                    </div>
                    <div class="view-content">
                        <p class="placeholder-text">{{ activeView }} 功能正在开发中...</p>
                    </div>
                </div>
            </main>
        </div>

        <!-- 状态栏 -->
        <footer class="status-bar">
            <div class="status-left">
                <span v-if="currentProject" class="status-item">
                    项目: {{ currentProject.name }}
                </span>
            </div>
            <div class="status-right">
                <span class="status-item">就绪</span>
            </div>
        </footer>

        <!-- 角色编辑对话框 -->
        <div v-if="showCharacterDialog" class="modal-overlay" @click="closeCharacterDialog">
            <div class="modal-dialog" @click.stop>
                <div class="modal-header">
                    <h3>{{ editingCharacter ? '编辑角色' : '新建角色' }}</h3>
                    <button class="modal-close" @click="closeCharacterDialog">×</button>
                </div>
                <div class="modal-body">
                    <form @submit.prevent="saveCharacter">
                        <div class="form-group">
                            <label>角色名称 *</label>
                            <input type="text" v-model="characterForm.name" required>
                        </div>
                        <div class="form-group">
                            <label>角色原型</label>
                            <input type="text" v-model="characterForm.archetype" placeholder="如：不情愿的英雄">
                        </div>
                        <div class="form-group">
                            <label>动机</label>
                            <textarea v-model="characterForm.motivation" placeholder="角色的主要动机和目标"></textarea>
                        </div>
                        <div class="form-group">
                            <label>外貌描述</label>
                            <textarea v-model="characterForm.appearance" placeholder="角色的外貌特征"></textarea>
                        </div>
                        <div class="form-group">
                            <label>初始情绪</label>
                            <input type="text" v-model="characterForm.emotion" placeholder="如：焦虑、兴奋、平静">
                        </div>
                        <div class="form-group">
                            <label>初始位置</label>
                            <select v-model="characterForm.location">
                                <option value="">选择位置</option>
                                <option v-for="location in locations" :key="location.id" :value="location.id">
                                    {{ location.name }}
                                </option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeCharacterDialog">取消</button>
                    <button class="btn btn-primary" @click="saveCharacter">保存</button>
                </div>
            </div>
        </div>

        <!-- 地点编辑对话框 -->
        <div v-if="showLocationDialog" class="modal-overlay" @click="closeLocationDialog">
            <div class="modal-dialog" @click.stop>
                <div class="modal-header">
                    <h3>{{ editingLocation ? '编辑地点' : '新建地点' }}</h3>
                    <button class="modal-close" @click="closeLocationDialog">×</button>
                </div>
                <div class="modal-body">
                    <form @submit.prevent="saveLocation">
                        <div class="form-group">
                            <label>地点名称 *</label>
                            <input type="text" v-model="locationForm.name" required>
                        </div>
                        <div class="form-group">
                            <label>地点类型</label>
                            <select v-model="locationForm.type">
                                <option value="">选择类型</option>
                                <option value="城市">城市</option>
                                <option value="建筑">建筑</option>
                                <option value="自然">自然环境</option>
                                <option value="室内">室内场所</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>描述</label>
                            <textarea v-model="locationForm.description" placeholder="地点的详细描述"></textarea>
                        </div>
                        <div class="form-group">
                            <label>氛围</label>
                            <input type="text" v-model="locationForm.atmosphere" placeholder="如：神秘、温馨、紧张">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeLocationDialog">取消</button>
                    <button class="btn btn-primary" @click="saveLocation">保存</button>
                </div>
            </div>
        </div>

        <!-- 组织编辑对话框 -->
        <div v-if="showOrganizationDialog" class="modal-overlay" @click="closeOrganizationDialog">
            <div class="modal-dialog" @click.stop>
                <div class="modal-header">
                    <h3>{{ editingOrganization ? '编辑组织' : '新建组织' }}</h3>
                    <button class="modal-close" @click="closeOrganizationDialog">×</button>
                </div>
                <div class="modal-body">
                    <form @submit.prevent="saveOrganization">
                        <div class="form-group">
                            <label>组织名称 *</label>
                            <input type="text" v-model="organizationForm.name" required>
                        </div>
                        <div class="form-group">
                            <label>组织类型</label>
                            <select v-model="organizationForm.type">
                                <option value="">选择类型</option>
                                <option value="政府">政府机构</option>
                                <option value="军事">军事组织</option>
                                <option value="商业">商业团体</option>
                                <option value="宗教">宗教组织</option>
                                <option value="学术">学术机构</option>
                                <option value="秘密">秘密组织</option>
                                <option value="犯罪">犯罪集团</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>组织描述</label>
                            <textarea v-model="organizationForm.description" placeholder="组织的详细描述、宗旨、历史等"></textarea>
                        </div>
                        <div class="form-group">
                            <label>领导者</label>
                            <select v-model="organizationForm.leader">
                                <option value="">选择领导者</option>
                                <option v-for="character in characters" :key="character.id" :value="character.name">
                                    {{ character.name }}
                                </option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>组织成员</label>
                            <div class="checkbox-group">
                                <label v-for="character in characters" :key="character.id" class="checkbox-label">
                                    <input type="checkbox" :value="character.name" v-model="organizationForm.members">
                                    {{ character.name }}
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>影响力等级</label>
                            <select v-model="organizationForm.influence_level">
                                <option value="">选择影响力</option>
                                <option value="local">地方性</option>
                                <option value="regional">区域性</option>
                                <option value="national">国家级</option>
                                <option value="international">国际性</option>
                                <option value="legendary">传奇级</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>组织状态</label>
                            <select v-model="organizationForm.status">
                                <option value="">选择状态</option>
                                <option value="active">活跃</option>
                                <option value="dormant">休眠</option>
                                <option value="declining">衰落</option>
                                <option value="rising">崛起</option>
                                <option value="disbanded">解散</option>
                                <option value="hidden">隐秘</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>总部位置</label>
                            <select v-model="organizationForm.headquarters">
                                <option value="">选择总部</option>
                                <option v-for="location in locations" :key="location.id" :value="location.name">
                                    {{ location.name }}
                                </option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>组织资源</label>
                            <textarea v-model="organizationForm.resources" placeholder="组织拥有的资源、财富、装备等"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeOrganizationDialog">取消</button>
                    <button class="btn btn-primary" @click="saveOrganization">保存</button>
                </div>
            </div>
        </div>

        <!-- 场景编辑对话框 -->
        <div v-if="showSceneDialog" class="modal-overlay" @click="closeSceneDialog">
            <div class="modal-dialog modal-large" @click.stop>
                <div class="modal-header">
                    <h3>{{ editingScene ? '编辑场景' : '新建场景' }}</h3>
                    <button class="modal-close" @click="closeSceneDialog">×</button>
                </div>
                <div class="modal-body">
                    <form @submit.prevent="saveScene">
                        <div class="form-group">
                            <label>场景ID *</label>
                            <input type="text" v-model="sceneForm.scene_id" required
                                   placeholder="如：sc_001_introduction">
                        </div>
                        <div class="form-group">
                            <label>故事线</label>
                            <select v-model="sceneForm.storyline">
                                <option value="main">主线</option>
                                <option value="side_quest_A">支线A</option>
                                <option value="side_quest_B">支线B</option>
                                <option value="flashback">回忆</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>参与角色</label>
                            <div class="checkbox-group">
                                <label v-for="character in characters" :key="character.id" class="checkbox-label">
                                    <input type="checkbox" :value="character.id" v-model="sceneForm.characters">
                                    {{ character.name }}
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>场景地点</label>
                            <select v-model="sceneForm.location">
                                <option value="">选择地点</option>
                                <option v-for="location in locations" :key="location.id" :value="location.id">
                                    {{ location.name }}
                                </option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>场景目标</label>
                            <div class="goals-editor">
                                <div v-for="(goal, index) in sceneForm.goals" :key="index" class="goal-item">
                                    <select v-model="goal.type" class="goal-type-select">
                                        <option value="exposition">说明</option>
                                        <option value="character_interaction">角色互动</option>
                                        <option value="plot_hook">情节钩子</option>
                                        <option value="conflict">冲突</option>
                                        <option value="resolution">解决</option>
                                    </select>
                                    <input type="text" v-model="goal.content" placeholder="目标描述" class="goal-content">
                                    <button type="button" @click="removeGoal(index)" class="btn-remove">×</button>
                                </div>
                                <button type="button" @click="addGoal" class="btn btn-small">添加目标</button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>场景简述</label>
                            <textarea v-model="sceneForm.description" placeholder="场景的简要描述"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeSceneDialog">取消</button>
                    <button class="btn btn-primary" @click="saveScene">保存</button>
                </div>
            </div>
        </div>

        <!-- API配置对话框 -->
        <div v-if="showApiDialog" class="modal-overlay" @click="closeApiDialog">
            <div class="modal-dialog modal-large" @click.stop>
                <div class="modal-header">
                    <h3>AI API 配置</h3>
                    <button class="modal-close" @click="closeApiDialog">×</button>
                </div>
                <div class="modal-body">
                    <form @submit.prevent="saveApiConfig">
                        <div class="api-config-tabs">
                            <div class="tab-header">
                                <button type="button" class="tab-button" :class="{active: activeApiTab === 'analysis'}" @click="activeApiTab = 'analysis'">
                                    分析API配置
                                </button>
                                <button type="button" class="tab-button" :class="{active: activeApiTab === 'generation'}" @click="activeApiTab = 'generation'">
                                    生成API配置
                                </button>
                            </div>

                            <!-- 分析API配置 -->
                            <div v-show="activeApiTab === 'analysis'" class="tab-content">
                                <h4>AI分析API配置</h4>
                                <p class="tab-description">用于场景分析和角色状态更新，建议使用较低的temperature值以确保分析结果的稳定性</p>

                                <div class="form-group">
                                    <label>API Base URL</label>
                                    <input type="url" v-model="analysisApiForm.base_url"
                                           placeholder="https://api.openai.com/v1">
                                    <small class="form-help">API服务器地址，留空使用默认OpenAI地址</small>
                                </div>
                                <div class="form-group">
                                    <label>API Key</label>
                                    <input type="password" v-model="analysisApiForm.api_key"
                                           placeholder="sk-... 或其他API密钥">
                                    <small class="form-help">请输入您的API密钥</small>
                                </div>
                                <div class="form-group">
                                    <label>模型名称</label>
                                    <input type="text" v-model="analysisApiForm.model"
                                           placeholder="gpt-3.5-turbo">
                                    <small class="form-help">可以是OpenAI模型或其他兼容模型</small>
                                </div>
                                <div class="form-group">
                                    <label>常用模型快选</label>
                                    <div class="model-buttons">
                                        <button type="button" class="btn btn-small" @click="setAnalysisModel('gpt-3.5-turbo')">GPT-3.5 Turbo</button>
                                        <button type="button" class="btn btn-small" @click="setAnalysisModel('gpt-4')">GPT-4</button>
                                        <button type="button" class="btn btn-small" @click="setAnalysisModel('gpt-4-turbo')">GPT-4 Turbo</button>
                                        <button type="button" class="btn btn-small" @click="setAnalysisModel('claude-3-sonnet')">Claude-3 Sonnet</button>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Temperature (创造性)</label>
                                    <input type="range" v-model="analysisApiForm.temperature" min="0" max="1" step="0.1">
                                    <div class="range-value">{{ analysisApiForm.temperature }}</div>
                                    <small class="form-help">分析建议使用较低值(0.1-0.3)确保结果稳定</small>
                                </div>
                            </div>

                            <!-- 生成API配置 -->
                            <div v-show="activeApiTab === 'generation'" class="tab-content">
                                <h4>AI生成API配置</h4>
                                <p class="tab-description">用于场景初稿生成，可以使用较高的temperature值以增加创造性</p>

                                <div class="form-group">
                                    <label>API Base URL</label>
                                    <input type="url" v-model="generationApiForm.base_url"
                                           placeholder="https://api.openai.com/v1">
                                    <small class="form-help">API服务器地址，留空使用默认OpenAI地址</small>
                                </div>
                                <div class="form-group">
                                    <label>API Key</label>
                                    <input type="password" v-model="generationApiForm.api_key"
                                           placeholder="sk-... 或其他API密钥">
                                    <small class="form-help">请输入您的API密钥</small>
                                </div>
                                <div class="form-group">
                                    <label>模型名称</label>
                                    <input type="text" v-model="generationApiForm.model"
                                           placeholder="gpt-3.5-turbo">
                                    <small class="form-help">可以是OpenAI模型或其他兼容模型</small>
                                </div>
                                <div class="form-group">
                                    <label>常用模型快选</label>
                                    <div class="model-buttons">
                                        <button type="button" class="btn btn-small" @click="setGenerationModel('gpt-3.5-turbo')">GPT-3.5 Turbo</button>
                                        <button type="button" class="btn btn-small" @click="setGenerationModel('gpt-4')">GPT-4</button>
                                        <button type="button" class="btn btn-small" @click="setGenerationModel('gpt-4-turbo')">GPT-4 Turbo</button>
                                        <button type="button" class="btn btn-small" @click="setGenerationModel('claude-3-sonnet')">Claude-3 Sonnet</button>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Temperature (创造性)</label>
                                    <input type="range" v-model="generationApiForm.temperature" min="0" max="1" step="0.1">
                                    <div class="range-value">{{ generationApiForm.temperature }}</div>
                                    <small class="form-help">生成建议使用中等到较高值(0.5-0.8)增加创造性</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <p class="form-note">
                                <strong>提示：</strong>您可以为分析和生成配置不同的API，也可以使用相同的API配置。
                                至少需要配置一个API的密钥才能使用AI功能。
                            </p>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeApiDialog">取消</button>
                    <button class="btn btn-primary" @click="saveApiConfig">保存</button>
                </div>
            </div>
        </div>

        <!-- 文本编辑器对话框 -->
        <div v-if="showEditorDialog" class="modal-overlay editor-overlay" @click="closeEditorDialog">
            <div class="modal-dialog editor-dialog" @click.stop>
                <div class="modal-header">
                    <h3>编辑草稿: {{ editingDraft?.title }}</h3>
                    <div class="editor-actions">
                        <button class="btn btn-success" @click="publishDraft" :disabled="!editorContent.trim()">
                            <span class="btn-icon">📤</span>
                            完成并提交
                        </button>
                        <button class="btn btn-secondary" @click="saveDraft">
                            <span class="btn-icon">💾</span>
                            保存草稿
                        </button>
                        <button class="modal-close" @click="closeEditorDialog">×</button>
                    </div>
                </div>
                <div class="modal-body editor-body">
                    <div class="editor-toolbar">
                        <button class="toolbar-btn" @click="formatText('bold')" title="粗体">
                            <strong>B</strong>
                        </button>
                        <button class="toolbar-btn" @click="formatText('italic')" title="斜体">
                            <em>I</em>
                        </button>
                        <button class="toolbar-btn" @click="formatText('underline')" title="下划线">
                            <u>U</u>
                        </button>
                        <div class="toolbar-separator"></div>
                        <button class="toolbar-btn" @click="insertText('dialogue')" title="插入对话">
                            💬
                        </button>
                        <button class="toolbar-btn" @click="insertText('description')" title="插入描述">
                            📝
                        </button>
                        <button class="toolbar-btn" @click="insertText('action')" title="插入动作">
                            🎬
                        </button>
                    </div>
                    <textarea
                        ref="editor"
                        v-model="editorContent"
                        class="text-editor"
                        placeholder="在这里编辑您的故事..."
                        @input="onEditorInput"
                        @keydown="onEditorKeydown">
                    </textarea>
                    <div class="editor-status">
                        <span class="word-count">字数: {{ wordCount }}</span>
                        <span class="save-status" :class="{ 'saved': isSaved, 'unsaved': !isSaved }">
                            {{ isSaved ? '已保存' : '未保存' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI生成状态显示 -->
        <div v-if="isGenerating" class="generating-overlay">
            <div class="generating-dialog">
                <div class="generating-spinner"></div>
                <div class="generating-text">{{ generatingText }}</div>
                <div class="generating-subtext">{{ generatingSubtext }}</div>
            </div>
        </div>

        <!-- 通知消息 -->
        <div v-if="notification.show" class="notification" :class="notification.type">
            <div class="notification-content">
                <span class="notification-icon">{{ notification.icon }}</span>
                <span class="notification-message">{{ notification.message }}</span>
                <button class="notification-close" @click="hideNotification">×</button>
            </div>
        </div>
    </div>

    <script src="scripts/app.js"></script>
</body>
</html>
